import { NextResponse } from "next/server"
import { logError } from "@/lib/simple-logger"

export async function POST(request: Request) {
  try {
    // Parse the request
    const { messages } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: "Valid messages array is required" }, { status: 400 })
    }

    // Get API key - use server-side only
    const apiKey = process.env.GEMINI_API_KEY

    if (!apiKey) {
      return NextResponse.json({ error: "Gemini API key not configured" }, { status: 500 })
    }

    // Extract system message and user messages
    const systemMessage = messages.find((msg) => msg.role === "system")
    const userMessages = messages.filter((msg) => msg.role !== "system")

    // Format messages for Gemini
    const geminiMessages = userMessages.map((msg) => ({
      role: msg.role === "assistant" ? "model" : msg.role,
      parts: [{ text: msg.content }],
    }))

    // Prepare request body
    const requestBody: any = {
      contents: geminiMessages,
      generationConfig: {
        maxOutputTokens: 300,
        temperature: 0.7,
      },
    }

    // Add system message as system instruction if present
    if (systemMessage) {
      requestBody.systemInstruction = {
        parts: [{ text: systemMessage.content }],
      }
    }

    // Call Gemini API
    const geminiResponse = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": apiKey,
        },
        body: JSON.stringify(requestBody),
      },
    )

    if (!geminiResponse.ok) {
      const errorText = await geminiResponse.text().catch(() => "Unknown error")
      logError("Gemini API error", { status: geminiResponse.status, error: errorText })
      return NextResponse.json({ error: `Gemini API error: ${geminiResponse.status}` }, { status: 502 })
    }

    const data = await geminiResponse.json()

    // Extract content from Gemini response
    const content = data.candidates?.[0]?.content?.parts?.[0]?.text || "No response generated"

    return NextResponse.json({
      content,
      model: "gemini-2.0",
      status: "success",
    })
  } catch (error: any) {
    logError("Gemini direct API error", { error: error.message, stack: error.stack })
    return NextResponse.json({ error: error.message || "Unknown error" }, { status: 500 })
  }
}
