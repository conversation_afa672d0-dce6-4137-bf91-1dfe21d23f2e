import { NextResponse } from "next/server"
import { testDeepSeekConnection } from "@/lib/deepseek-client"
import { logger } from "@/lib/api/logger"

export async function GET() {
  try {
    // Test the DeepSeek connection
    const result = await testDeepSeekConnection()

    // Log the result
    if (result.success) {
      logger.info("DeepSeek connection test successful", { message: result.message })
    } else {
      logger.warn("DeepSeek connection test failed", { message: result.message })
    }

    // Return the result
    return NextResponse.json(result)
  } catch (error: any) {
    // Log the error
    logger.error("DeepSeek test API error", { error: error.message, stack: error.stack })

    // Return error response
    return NextResponse.json(
      { success: false, message: error.message || "Failed to test DeepSeek connection" },
      { status: 500 },
    )
  }
}
