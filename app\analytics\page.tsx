"use client"

import { useState, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useEffect } from "react"
import { useAnalyticsDataFallback } from "@/hooks/use-analytics-data-fallback"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON>bsContent } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { BookOpen, Trophy, BarChart3, Calendar, Target, Activity, CheckCircle, AlertTriangle, Lightbulb, Download } from "lucide-react"
import { useGamificationStore } from "@/lib/stores/gamification-store"
import { motion } from "framer-motion"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts"
import { EnhancedAnalyticsService } from "@/lib/services/enhanced-analytics-service"
import { AnalyticsExport } from "@/components/analytics/analytics-export"
import { UnifiedOverviewMetrics } from "@/components/analytics/unified-overview-metrics"
import { UnifiedJournalAnalytics } from "@/components/analytics/unified-journal-analytics"
import { UnifiedGoalAnalytics } from "@/components/analytics/unified-goal-analytics"

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState("overview")

  // Use the fallback hook for more reliable data loading
  const {
    entries,
    goals,
    isLoading,
    hasData,
    dataSource,
    errors
  } = useAnalyticsDataFallback()

  const { level } = useGamificationStore()

  // Debug logging
  console.log('Analytics - Fallback Data:', {
    entries: entries.length,
    goals: goals.length,
    isLoading,
    hasData,
    dataSource
  })



  // Show loading only if still loading
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto py-12 px-4">
          <div className="max-w-6xl mx-auto space-y-12">
            {/* Loading Header */}
            <div className="space-y-6 text-center">
              <div className="h-16 bg-muted/30 rounded-lg animate-pulse max-w-md mx-auto"></div>
              <div className="h-6 bg-muted/20 rounded-lg animate-pulse max-w-2xl mx-auto"></div>
            </div>

            {/* Loading Tabs */}
            <div className="flex justify-center">
              <div className="h-12 bg-muted/30 rounded-xl animate-pulse w-96"></div>
            </div>

            {/* Loading Content */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="h-64 bg-muted/20 rounded-lg animate-pulse"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show error state if there are critical errors
  if ((errors.journal || errors.goals) && !isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto py-12 px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center py-20 space-y-8">
              <div className="mx-auto w-20 h-20 bg-red-50 dark:bg-red-950/20 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-10 h-10 text-red-500" />
              </div>
              <div className="space-y-4">
                <h3 className="text-2xl font-medium">Error Loading Analytics</h3>
                <p className="text-muted-foreground text-lg max-w-md mx-auto leading-relaxed">
                  There was an issue loading your analytics data. Please try refreshing the page.
                </p>
                {errors.journal && (
                  <p className="text-sm text-red-500">Journal Error: {errors.journal.message || errors.journal}</p>
                )}
                {errors.goals && (
                  <p className="text-sm text-red-500">Goals Error: {errors.goals}</p>
                )}
              </div>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    )
  }



  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-12 px-4">
        <motion.div
          className="max-w-6xl mx-auto space-y-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {/* Minimalist Header */}
          <motion.div
            className="text-center space-y-6"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <h1 className="text-4xl md:text-5xl font-light tracking-tight text-foreground">
              Analytics
            </h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto leading-relaxed">
              Visualize your reflections, track your progress, and explore your gamification journey.
            </p>
          </motion.div>

          {/* Clean Data Summary */}
          <motion.div
            className="flex items-center justify-center gap-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="flex items-center gap-2 px-4 py-2 bg-muted/30 rounded-lg">
              <BarChart3 className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">
                {entries.length} Entries • {goals.length} Goals
              </span>
            </div>
          </motion.div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-12">
            <div className="flex justify-center">
              <TabsList className="bg-muted/30 border-0">
                <TabsTrigger
                  value="overview"
                  className="flex items-center gap-2 data-[state=active]:bg-background"
                >
                  <BarChart3 className="h-4 w-4" />
                  Overview
                </TabsTrigger>

                <TabsTrigger
                  value="journal"
                  className="flex items-center gap-2 data-[state=active]:bg-background"
                >
                  <BookOpen className="h-4 w-4" />
                  Journal
                </TabsTrigger>

                <TabsTrigger
                  value="goals"
                  className="flex items-center gap-2 data-[state=active]:bg-background"
                >
                  <Trophy className="h-4 w-4" />
                  Goals
                </TabsTrigger>

                <TabsTrigger
                  value="export"
                  className="flex items-center gap-2 data-[state=active]:bg-background"
                >
                  <Download className="h-4 w-4" />
                  Export
                </TabsTrigger>
              </TabsList>
            </div>

        {/* Empty State - Only show if not loading and truly no data */}
        {!isLoading && !hasData && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center py-16 space-y-6"
          >
            <div className="mx-auto w-24 h-24 bg-muted/30 rounded-full flex items-center justify-center">
              <BarChart3 className="w-12 h-12 text-muted-foreground/50" />
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-semibold">No Analytics Data Yet</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                Start journaling and creating goals to see your analytics and insights here.
              </p>
              <p className="text-xs text-muted-foreground">
                Debug: Entries: {entries.length}, Goals: {goals.length}, Loading: {isLoading ? 'Yes' : 'No'}, Source: {dataSource}
              </p>
            </div>
            <div className="flex gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2 bg-primary text-primary-foreground rounded-lg font-medium"
                onClick={() => window.location.href = '/journal/new?clearDraft=true'}
              >
                Create Journal Entry
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2 border border-border rounded-lg font-medium"
                onClick={() => window.location.href = '/goals/new'}
              >
                Create Goal
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2 border border-border rounded-lg font-medium"
                onClick={() => window.location.href = '/debug-data'}
              >
                Debug Data
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2 border border-orange-500 text-orange-500 rounded-lg font-medium"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </motion.button>
            </div>
          </motion.div>
        )}

        {!isLoading && hasData && (
          <>
            <TabsContent value="overview">
              <UnifiedOverviewMetrics entries={entries} goals={goals} />
            </TabsContent>

            <TabsContent value="journal">
              <UnifiedJournalAnalytics entries={entries} />
            </TabsContent>

            <TabsContent value="goals">
              <UnifiedGoalAnalytics goals={goals} />
            </TabsContent>

        <TabsContent value="export">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
            <AnalyticsExport entries={entries} goals={goals} />
          </motion.div>
        </TabsContent>
        </>
        )}
      </Tabs>
        </motion.div>
      </div>
    </div>
  )
}
