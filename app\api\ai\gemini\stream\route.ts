import type { NextRequest } from "next/server"
import { streamGeminiChatCompletion } from "@/lib/gemini-client"
import { logger } from "@/lib/api/logger"
import { getCurrentUser } from "@/lib/auth-service"

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser()
    if (!user) {
      return new Response(JSON.stringify({ error: "Not authenticated" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      })
    }

    // Parse request body
    const body = await request.json()
    const { messages, model, temperature, max_tokens, top_p, stop } = body

    // Validate required fields
    if (!messages || !Array.isArray(messages)) {
      return new Response(JSON.stringify({ error: "Messages are required and must be an array" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      })
    }

    // Log the request
    logger.info("Gemini stream API request", {
      userId: user.id,
      model: model || "gemini-2.0-flash",
      messageCount: messages.length,
    })

    // Stream completion
    const stream = await streamGeminiChatCompletion({
      messages,
      model: model || "gemini-2.0-flash",
      temperature,
      max_tokens,
      top_p,
      stream: true,
      stop,
    })

    // Return the stream
    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    })
  } catch (error: any) {
    // Log the error
    logger.error("Gemini stream API error", { error: error.message, stack: error.stack })

    // Return error response
    return new Response(JSON.stringify({ error: error.message || "Failed to generate stream" }), {
      status: error.status || 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}
