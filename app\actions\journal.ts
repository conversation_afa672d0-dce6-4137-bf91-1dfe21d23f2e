"use server"

import { v4 as uuidv4 } from "uuid"
import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { auth } from "@/lib/auth"
import * as db from "@/lib/database"
import type { KeyTakeaway } from "@/components/key-takeaways"

// Mock data storage (in a real app, this would be a database)
let MOCK_ENTRIES = [
  {
    id: "1",
    title: "First Journal Entry",
    content: "<p>This is my first journal entry. I'm excited to start journaling!</p>",
    created_at: "2023-05-15T10:30:00Z",
    user_id: "user123",
    mood: 4,
    takeaways: JSON.stringify([
      {
        id: "101",
        content: "Journaling helps clear my mind",
        type: "insight",
        created_at: "2023-05-15T10:35:00Z",
      },
      {
        id: "102",
        content: "Set aside 10 minutes each morning for journaling",
        type: "action",
        completed: false,
        created_at: "2023-05-15T10:36:00Z",
      },
    ]),
  },
  {
    id: "2",
    title: "Reflections on the Week",
    content: "<p>This week was challenging but rewarding. I learned a lot about myself.</p>",
    created_at: "2023-05-20T18:45:00Z",
    user_id: "user123",
    mood: 3,
    takeaways: JSON.stringify([
      {
        id: "201",
        content: "I handle stress better when I exercise regularly",
        type: "insight",
        created_at: "2023-05-20T18:50:00Z",
      },
      {
        id: "202",
        content: "Schedule at least 3 workouts next week",
        type: "action",
        completed: true,
        created_at: "2023-05-20T18:52:00Z",
      },
    ]),
  },
]

interface JournalEntry {
  id?: string
  title?: string
  content?: string
  mood?: number
  takeaways?: string
}

// Create a new journal entry
export async function createJournalEntry(formData: FormData) {
  const session = await auth()
  if (!session?.user) {
    redirect("/login")
  }

  const userId = session.user.id
  const title = formData.get("title") as string
  const content = formData.get("content") as string
  const mood = (formData.get("mood") as string) || null
  const tagsString = formData.get("tags") as string
  const tags = tagsString ? tagsString.split(",").map((tag) => tag.trim()) : []
  const date = (formData.get("date") as string) || new Date().toISOString().split("T")[0]

  // Handle location if provided
  let location = null
  const locationName = formData.get("locationName") as string
  const locationLat = formData.get("locationLat") as string
  const locationLng = formData.get("locationLng") as string

  if (locationName && locationLat && locationLng) {
    location = {
      name: locationName,
      lat: Number.parseFloat(locationLat),
      lng: Number.parseFloat(locationLng),
    }
  }

  // Handle attachments if provided
  let attachments = null
  const attachmentsJson = formData.get("attachments") as string

  if (attachmentsJson) {
    try {
      attachments = JSON.parse(attachmentsJson)
    } catch (error) {
      console.error("Failed to parse attachments:", error)
    }
  }

  try {
    const entry = await db.createJournalEntry({
      id: uuidv4(),
      user_id: userId,
      title,
      content,
      mood,
      tags,
      location,
      attachments,
      date,
    })

    revalidatePath("/journal")
    return { success: true, entryId: entry.id }
  } catch (error) {
    console.error("Failed to create journal entry:", error)
    return { success: false, error: "Failed to create journal entry" }
  }
}

// Update an existing journal entry
export async function updateJournalEntry(entryId: string, formData: FormData) {
  const session = await auth()
  if (!session?.user) {
    redirect("/login")
  }

  const userId = session.user.id
  const title = formData.get("title") as string
  const content = formData.get("content") as string
  const mood = (formData.get("mood") as string) || null
  const tagsString = formData.get("tags") as string
  const tags = tagsString ? tagsString.split(",").map((tag) => tag.trim()) : []
  const date = (formData.get("date") as string) || new Date().toISOString().split("T")[0]

  // Handle location if provided
  let location = null
  const locationName = formData.get("locationName") as string
  const locationLat = formData.get("locationLat") as string
  const locationLng = formData.get("locationLng") as string

  if (locationName && locationLat && locationLng) {
    location = {
      name: locationName,
      lat: Number.parseFloat(locationLat),
      lng: Number.parseFloat(locationLng),
    }
  }

  // Handle attachments if provided
  let attachments = null
  const attachmentsJson = formData.get("attachments") as string

  if (attachmentsJson) {
    try {
      attachments = JSON.parse(attachmentsJson)
    } catch (error) {
      console.error("Failed to parse attachments:", error)
    }
  }

  try {
    await db.updateJournalEntry(entryId, userId, {
      title,
      content,
      mood,
      tags,
      location,
      attachments,
      date,
    })

    revalidatePath("/journal")
    revalidatePath(`/journal/${entryId}`)
    return { success: true }
  } catch (error) {
    console.error("Failed to update journal entry:", error)
    return { success: false, error: "Failed to update journal entry" }
  }
}

// Delete a journal entry
export async function deleteJournalEntry(entryId: string) {
  const session = await auth()
  if (!session?.user) {
    redirect("/login")
  }

  const userId = session.user.id

  try {
    await db.deleteJournalEntry(entryId, userId)
    revalidatePath("/journal")
    return { success: true }
  } catch (error) {
    console.error("Failed to delete journal entry:", error)
    return { success: false, error: "Failed to delete journal entry" }
  }
}

// Create a new folder
export async function createFolder(formData: FormData) {
  const session = await auth()
  if (!session?.user) {
    redirect("/login")
  }

  const userId = session.user.id
  const name = formData.get("name") as string
  const color = (formData.get("color") as string) || "#4f46e5"

  try {
    const folder = await db.createFolder({
      id: uuidv4(),
      user_id: userId,
      name,
      color,
    })

    revalidatePath("/journal/folders")
    return { success: true, folderId: folder.id }
  } catch (error) {
    console.error("Failed to create folder:", error)
    return { success: false, error: "Failed to create folder" }
  }
}

// Update a folder
export async function updateFolder(folderId: string, formData: FormData) {
  const session = await auth()
  if (!session?.user) {
    redirect("/login")
  }

  const userId = session.user.id
  const name = formData.get("name") as string
  const color = (formData.get("color") as string) || "#4f46e5"

  try {
    await db.updateFolder(folderId, userId, {
      name,
      color,
    })

    revalidatePath("/journal/folders")
    return { success: true }
  } catch (error) {
    console.error("Failed to update folder:", error)
    return { success: false, error: "Failed to update folder" }
  }
}

// Delete a folder
export async function deleteFolder(folderId: string) {
  const session = await auth()
  if (!session?.user) {
    redirect("/login")
  }

  const userId = session.user.id

  try {
    await db.deleteFolder(folderId, userId)
    revalidatePath("/journal/folders")
    return { success: true }
  } catch (error) {
    console.error("Failed to delete folder:", error)
    return { success: false, error: "Failed to delete folder" }
  }
}

// Add an entry to a folder
export async function addEntryToFolder(folderId: string, entryId: string) {
  const session = await auth()
  if (!session?.user) {
    redirect("/login")
  }

  try {
    await db.addEntryToFolder(folderId, entryId)
    revalidatePath("/journal/folders")
    revalidatePath(`/journal/${entryId}`)
    return { success: true }
  } catch (error) {
    console.error("Failed to add entry to folder:", error)
    return { success: false, error: "Failed to add entry to folder" }
  }
}

// Remove an entry from a folder
export async function removeEntryFromFolder(folderId: string, entryId: string) {
  const session = await auth()
  if (!session?.user) {
    redirect("/login")
  }

  try {
    await db.removeEntryFromFolder(folderId, entryId)
    revalidatePath("/journal/folders")
    revalidatePath(`/journal/${entryId}`)
    return { success: true }
  } catch (error) {
    console.error("Failed to remove entry from folder:", error)
    return { success: false, error: "Failed to remove entry from folder" }
  }
}

export async function createJournalEntryMock(data: JournalEntry) {
  const newEntry = {
    id: Date.now().toString(),
    user_id: "user123", // Mock user ID
    title: data.title || "Untitled Entry",
    content: data.content || "",
    mood: data.mood,
    takeaways: data.takeaways || "[]",
    created_at: new Date().toISOString(),
  }

  // Add to mock data
  MOCK_ENTRIES = [newEntry, ...MOCK_ENTRIES]

  revalidatePath("/journal")
  return { id: newEntry.id }
}

export async function updateJournalEntryMock(data: JournalEntry) {
  if (!data.id) {
    throw new Error("Journal entry ID is required")
  }

  // Find and update the entry in mock data
  MOCK_ENTRIES = MOCK_ENTRIES.map((entry) => {
    if (entry.id === data.id) {
      return {
        ...entry,
        ...(data.title !== undefined && { title: data.title }),
        ...(data.content !== undefined && { content: data.content }),
        ...(data.mood !== undefined && { mood: data.mood }),
        ...(data.takeaways !== undefined && { takeaways: data.takeaways }),
      }
    }
    return entry
  })

  revalidatePath(`/journal/${data.id}`)
  revalidatePath("/journal")
}

export async function deleteJournalEntryMock(id: string) {
  // Remove from mock data
  MOCK_ENTRIES = MOCK_ENTRIES.filter((entry) => entry.id !== id)

  revalidatePath("/journal")
  redirect("/journal")
}

export async function getJournalEntries() {
  return MOCK_ENTRIES
}

export async function updateJournalEntryTakeaways(id: string, takeaways: KeyTakeaway[]) {
  // Find and update the entry in mock data
  MOCK_ENTRIES = MOCK_ENTRIES.map((entry) => {
    if (entry.id === id) {
      return {
        ...entry,
        takeaways: JSON.stringify(takeaways),
      }
    }
    return entry
  })

  revalidatePath(`/journal/${id}`)
}

export async function searchJournalEntries(query: string) {
  const lowerQuery = query.toLowerCase()
  return MOCK_ENTRIES.filter(
    (entry) => entry.title.toLowerCase().includes(lowerQuery) || entry.content.toLowerCase().includes(lowerQuery),
  ).map((entry) => ({
    id: entry.id,
    title: entry.title,
    content: entry.content,
    created_at: entry.created_at,
  }))
}

export async function searchTakeaways(query: string) {
  const lowerQuery = query.toLowerCase()

  // Filter entries with takeaways
  const results = MOCK_ENTRIES.map((entry) => {
    try {
      const takeaways = JSON.parse(entry.takeaways) as KeyTakeaway[]
      const matchingTakeaways = takeaways.filter((takeaway) => takeaway.content.toLowerCase().includes(lowerQuery))

      if (matchingTakeaways.length > 0) {
        return {
          entry_id: entry.id,
          entry_title: entry.title,
          created_at: entry.created_at,
          takeaways: matchingTakeaways,
        }
      }
      return null
    } catch (e) {
      console.error("Error parsing takeaways:", e)
      return null
    }
  }).filter(Boolean)

  return results
}
