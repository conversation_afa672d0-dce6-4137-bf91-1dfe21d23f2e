"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { createClient } from "@/lib/supabase/client"

interface ApiLog {
  id: string
  endpoint: string
  request_payload: any
  response_status: number
  error_message: string | null
  processing_time_ms: number
  created_at: string
  ip_address: string
}

export default function ApiLogsPage() {
  const [logs, setLogs] = useState<ApiLog[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(0)
  const [hasMore, setHasMore] = useState(true)
  const pageSize = 20

  const fetchLogs = async (pageIndex: number) => {
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()

      const { data, error } = await supabase
        .from("api_logs")
        .select("*")
        .order("created_at", { ascending: false })
        .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1)

      if (error) {
        throw error
      }

      if (data) {
        if (pageIndex === 0) {
          setLogs(data)
        } else {
          setLogs((prev) => [...prev, ...data])
        }

        setHasMore(data.length === pageSize)
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch API logs")
      console.error("Error fetching API logs:", err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchLogs(page)
  }, [page])

  const loadMore = () => {
    setPage((prev) => prev + 1)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">API Usage Logs</h1>

      <Card>
        <CardHeader>
          <CardTitle>API Request Logs</CardTitle>
        </CardHeader>
        <CardContent>
          {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{error}</div>}

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Time</TableHead>
                  <TableHead>Endpoint</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Processing Time</TableHead>
                  <TableHead>IP Address</TableHead>
                  <TableHead>Error</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {logs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>{formatDate(log.created_at)}</TableCell>
                    <TableCell>{log.endpoint}</TableCell>
                    <TableCell>
                      <span className={log.response_status >= 400 ? "text-red-500" : "text-green-500"}>
                        {log.response_status || "N/A"}
                      </span>
                    </TableCell>
                    <TableCell>{log.processing_time_ms}ms</TableCell>
                    <TableCell>{log.ip_address}</TableCell>
                    <TableCell className="text-red-500">{log.error_message || "-"}</TableCell>
                  </TableRow>
                ))}

                {loading && (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">
                      Loading...
                    </TableCell>
                  </TableRow>
                )}

                {!loading && logs.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">
                      No API logs found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {hasMore && (
            <div className="mt-4 text-center">
              <Button onClick={loadMore} disabled={loading}>
                {loading ? "Loading..." : "Load More"}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
