import { DeepHermesInfo } from "@/components/deephermes-info"
import { Llama3Info } from "@/components/llama3-info"
import { DeepseekV3Info } from "@/components/deepseek-v3-info"
import { DeepseekR1Info } from "@/components/deepseek-r1-info"
import { GeminiInfo } from "@/components/gemini-info"

export default function AIModelsPage() {
  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">AI Models</h1>
      <p className="text-lg mb-8">
        Reflect uses multiple AI models to provide the best experience for different types of tasks. Each model has its
        own strengths and capabilities.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
        <DeepHermesInfo />
        <Llama3Info />
      </div>

      <h2 className="text-2xl font-bold mb-4">Additional Models</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <DeepseekV3Info />
        <DeepseekR1Info />
        <GeminiInfo />
      </div>
    </div>
  )
}
