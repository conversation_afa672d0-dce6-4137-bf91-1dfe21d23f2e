"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/components/ui/use-toast"
import { toggleMaintenanceMode, addAllowedUser, removeAllowedUser } from "@/lib/actions/maintenance-actions"
import { getSupabaseClient } from "@/lib/supabase/client"
import { format } from "date-fns"
import { Loader2, Trash2 } from "lucide-react"

interface MaintenanceMode {
  id: number
  is_active: boolean
  reason: string | null
  estimated_completion: string | null
  created_at: string
  created_by: string | null
  updated_at: string
  updated_by: string | null
}

interface MaintenanceModeLog {
  id: number
  action: "ENABLED" | "DISABLED" | "UPDATED"
  reason: string | null
  estimated_completion: string | null
  created_at: string
  created_by: string | null
  user_email?: string
}

interface AllowedUser {
  id: number
  user_id: string
  created_at: string
  created_by: string | null
  user_email?: string
}

export default function MaintenanceAdminPage() {
  const [maintenanceStatus, setMaintenanceStatus] = useState<MaintenanceMode | null>(null)
  const [maintenanceLogs, setMaintenanceLogs] = useState<MaintenanceModeLog[]>([])
  const [allowedUsers, setAllowedUsers] = useState<AllowedUser[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [reason, setReason] = useState("")
  const [estimatedCompletion, setEstimatedCompletion] = useState("")
  const [newUserEmail, setNewUserEmail] = useState("")
  const [addingUser, setAddingUser] = useState(false)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    setLoading(true)
    const supabase = getSupabaseClient()

    // Fetch maintenance status
    const { data: statusData } = await supabase
      .from("maintenance_mode")
      .select("*")
      .order("id", { ascending: false })
      .limit(1)
      .single()

    if (statusData) {
      setMaintenanceStatus(statusData as MaintenanceMode)
      setReason(statusData.reason || "")
      setEstimatedCompletion(
        statusData.estimated_completion ? new Date(statusData.estimated_completion).toISOString().slice(0, 16) : "",
      )
    }

    // Fetch maintenance logs
    const { data: logsData } = await supabase
      .from("maintenance_mode_logs")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(10)

    if (logsData) {
      // Fetch user emails for logs
      const logs = logsData as MaintenanceModeLog[]
      const userIds = logs.map((log) => log.created_by).filter(Boolean) as string[]

      if (userIds.length > 0) {
        const { data: userData } = await supabase.from("user_profiles").select("user_id, email").in("user_id", userIds)

        if (userData) {
          const userMap = new Map(userData.map((user) => [user.user_id, user.email]))
          logs.forEach((log) => {
            if (log.created_by) {
              log.user_email = userMap.get(log.created_by) || "Unknown"
            }
          })
        }
      }

      setMaintenanceLogs(logs)
    }

    // Fetch allowed users
    const { data: allowedData } = await supabase
      .from("maintenance_mode_allowed_users")
      .select("*")
      .order("created_at", { ascending: false })

    if (allowedData) {
      // Fetch user emails
      const users = allowedData as AllowedUser[]
      const userIds = users.map((user) => user.user_id)

      if (userIds.length > 0) {
        const { data: userData } = await supabase.from("user_profiles").select("user_id, email").in("user_id", userIds)

        if (userData) {
          const userMap = new Map(userData.map((user) => [user.user_id, user.email]))
          users.forEach((user) => {
            user.user_email = userMap.get(user.user_id) || "Unknown"
          })
        }
      }

      setAllowedUsers(users)
    }

    setLoading(false)
  }

  const handleToggleMaintenance = async () => {
    if (!maintenanceStatus) return

    setSubmitting(true)

    const newStatus = !maintenanceStatus.is_active
    const result = await toggleMaintenanceMode(
      newStatus,
      newStatus ? reason : null,
      newStatus && estimatedCompletion ? new Date(estimatedCompletion).toISOString() : null,
    )

    if (result.success) {
      toast({
        title: "Success",
        description: result.message,
      })
      fetchData()
    } else {
      toast({
        title: "Error",
        description: result.error || "Failed to toggle maintenance mode",
        variant: "destructive",
      })
    }

    setSubmitting(false)
  }

  const handleAddAllowedUser = async () => {
    if (!newUserEmail) return

    setAddingUser(true)

    // First, find the user ID from the email
    const supabase = getSupabaseClient()
    const { data: userData, error: userError } = await supabase
      .from("user_profiles")
      .select("user_id")
      .eq("email", newUserEmail)
      .limit(1)
      .single()

    if (userError || !userData) {
      toast({
        title: "Error",
        description: "User not found with that email",
        variant: "destructive",
      })
      setAddingUser(false)
      return
    }

    // Add the user to allowed users
    const result = await addAllowedUser(userData.user_id)

    if (result.success) {
      toast({
        title: "Success",
        description: result.message,
      })
      setNewUserEmail("")
      fetchData()
    } else {
      toast({
        title: "Error",
        description: result.error || "Failed to add user",
        variant: "destructive",
      })
    }

    setAddingUser(false)
  }

  const handleRemoveAllowedUser = async (userId: string) => {
    const result = await removeAllowedUser(userId)

    if (result.success) {
      toast({
        title: "Success",
        description: result.message,
      })
      fetchData()
    } else {
      toast({
        title: "Error",
        description: result.error || "Failed to remove user",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="mb-6 text-3xl font-bold">Maintenance Mode Management</h1>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Maintenance Status</CardTitle>
            <CardDescription>Toggle maintenance mode and set details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="maintenance-toggle">Maintenance Mode</Label>
              <Switch
                id="maintenance-toggle"
                checked={maintenanceStatus?.is_active || false}
                onCheckedChange={handleToggleMaintenance}
                disabled={submitting}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reason">Reason for Maintenance</Label>
              <Textarea
                id="reason"
                placeholder="Enter the reason for maintenance"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="estimated-completion">Estimated Completion</Label>
              <Input
                id="estimated-completion"
                type="datetime-local"
                value={estimatedCompletion}
                onChange={(e) => setEstimatedCompletion(e.target.value)}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleToggleMaintenance} disabled={submitting} className="w-full">
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : maintenanceStatus?.is_active ? (
                "Disable Maintenance Mode"
              ) : (
                "Enable Maintenance Mode"
              )}
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Allowed Users</CardTitle>
            <CardDescription>Users who can access the site during maintenance</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <Input placeholder="User email" value={newUserEmail} onChange={(e) => setNewUserEmail(e.target.value)} />
              <Button onClick={handleAddAllowedUser} disabled={addingUser || !newUserEmail}>
                {addingUser ? <Loader2 className="h-4 w-4 animate-spin" /> : "Add"}
              </Button>
            </div>

            {allowedUsers.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Added On</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {allowedUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>{user.user_email}</TableCell>
                      <TableCell>{format(new Date(user.created_at), "MMM d, yyyy")}</TableCell>
                      <TableCell>
                        <Button variant="ghost" size="icon" onClick={() => handleRemoveAllowedUser(user.user_id)}>
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-center text-sm text-gray-500">No allowed users yet</p>
            )}
          </CardContent>
        </Card>
      </div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Maintenance History</CardTitle>
          <CardDescription>Recent maintenance mode changes</CardDescription>
        </CardHeader>
        <CardContent>
          {maintenanceLogs.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Action</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Estimated Completion</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>By User</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {maintenanceLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <span
                        className={`inline-block rounded-full px-2 py-1 text-xs font-semibold ${
                          log.action === "ENABLED"
                            ? "bg-red-100 text-red-800"
                            : log.action === "DISABLED"
                              ? "bg-green-100 text-green-800"
                              : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {log.action}
                      </span>
                    </TableCell>
                    <TableCell>{log.reason || "-"}</TableCell>
                    <TableCell>
                      {log.estimated_completion
                        ? format(new Date(log.estimated_completion), "MMM d, yyyy h:mm a")
                        : "-"}
                    </TableCell>
                    <TableCell>{format(new Date(log.created_at), "MMM d, yyyy h:mm a")}</TableCell>
                    <TableCell>{log.user_email || "-"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-center text-sm text-gray-500">No maintenance history yet</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
