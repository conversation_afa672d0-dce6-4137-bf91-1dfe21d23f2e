import { auth as clerkAuth } from "@clerk/nextjs/server"
import { logger } from "@/lib/logger"
import { checkRateLimit } from "@/lib/api/rate-limiter"

export async function POST(request: Request) {
  try {
    // Check authentication
    const { userId } = await clerkAuth()
    if (!userId) {
      return new Response("Unauthorized", { status: 401 })
    }

    // Check rate limit
    const rateLimitResult = await checkRateLimit(request, "ai-chat-stream", userId, {
      maxRequests: 20, // 20 streaming requests per hour per user
      windowMs: 60 * 60 * 1000
    })
    if (!rateLimitResult.allowed) {
      return new Response(
        JSON.stringify({
          error: "Rate limit exceeded",
          resetAt: rateLimitResult.resetAt.toISOString(),
        }),
        {
          status: 429,
          headers: { "Content-Type": "application/json" }
        }
      )
    }

    // Parse request body
    const { messages, model = "openrouter/deepseek-r1", temperature = 0.7, max_tokens = 1000 } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return new Response("Messages array is required", { status: 400 })
    }

    // Validate message content
    if (messages.length === 0) {
      return new Response("At least one message is required", { status: 400 })
    }

    // Validate message structure and content length
    for (const message of messages) {
      if (!message.role || !message.content) {
        return new Response("Invalid message format", { status: 400 })
      }
      if (typeof message.content !== 'string' || message.content.length > 10000) {
        return new Response("Message content too long", { status: 400 })
      }
    }

    // Check if OpenRouter API key is available
    const apiKey = process.env.OPENROUTER_API_KEY
    if (!apiKey) {
      return new Response("OpenRouter API key not configured", { status: 500 })
    }

    // Map model names to OpenRouter format
    const modelMap: Record<string, string> = {
      "openrouter/deepseek-r1": "deepseek/deepseek-r1",
      "openrouter/deepseek-v3": "deepseek/deepseek-v3", 
      "openrouter/llama-3-8b-instruct": "meta-llama/llama-3-8b-instruct",
      "openrouter/deephermes-3-mistral-24b": "cognitivecomputations/dolphin-2.9.4-llama3.1-8b",
      "openrouter/gemma-7b-it": "google/gemma-7b-it",
      "openrouter/xai-grok-1": "x-ai/grok-beta"
    }

    const openrouterModel = modelMap[model] || "deepseek/deepseek-r1"

    logger.info("Making streaming OpenRouter API call", {
      model: openrouterModel,
      messageCount: messages.length,
      userId
    })

    // Make streaming request to OpenRouter
    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json",
        "HTTP-Referer": process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
        "X-Title": "Reflect Journal App"
      },
      body: JSON.stringify({
        model: openrouterModel,
        messages,
        temperature,
        max_tokens,
        stream: true
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      logger.error("OpenRouter streaming API error", {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })
      
      return new Response(`OpenRouter API error: ${response.status} ${response.statusText}`, {
        status: response.status
      })
    }

    // Create a readable stream to forward the response
    const stream = new ReadableStream({
      start(controller) {
        const reader = response.body?.getReader()
        if (!reader) {
          controller.close()
          return
        }

        function pump(): Promise<void> {
          return reader.read().then(({ done, value }) => {
            if (done) {
              controller.close()
              return
            }
            
            controller.enqueue(value)
            return pump()
          }).catch((error) => {
            logger.error("Stream reading error:", error)
            controller.error(error)
          })
        }

        return pump()
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error: any) {
    logger.error("Streaming chat API error:", error)
    
    return new Response(error.message || "Internal server error", {
      status: 500
    })
  }
}
