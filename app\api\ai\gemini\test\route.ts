import { NextResponse } from "next/server"

import { getCurrentUser } from "@/lib/session"
import { logger } from "@/lib/utils"

export async function GET() {
  try {
    // Check authentication
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    // Get the API key
    const apiKey = process.env.GEMINI_API_KEY

    if (!apiKey) {
      return NextResponse.json(
        {
          success: false,
          message: "GEMINI_API_KEY is not defined in environment variables",
        },
        { status: 400 },
      )
    }

    // Check if it's an OpenRouter key
    const isOpenRouterKey = apiKey.startsWith("sk-or-")

    // Use the appropriate endpoint based on the key type
    const endpoint = isOpenRouterKey
      ? "https://openrouter.ai/api/v1/models"
      : `https://generativelanguage.googleapis.com/v1/models?key=${apiKey}`

    const headers: HeadersInit = {
      "Content-Type": "application/json",
    }

    // Add OpenRouter specific headers if needed
    if (isOpenRouterKey) {
      headers["Authorization"] = `Bearer ${apiKey}`
      headers["HTTP-Referer"] = process.env.NEXT_PUBLIC_SITE_URL || "https://reflect.vercel.app"
      headers["X-Title"] = "Reflect Journal App"
    }

    logger.info("Testing Gemini API connection", {
      userId: user.id,
      endpoint: endpoint.replace(apiKey, "[REDACTED]"),
    })

    // Make a test request to the API
    const response = await fetch(endpoint, {
      method: "GET",
      headers: headers,
      cache: "no-store",
    })

    if (!response.ok) {
      const errorText = await response.text()
      logger.error("Gemini API test failed", {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      })

      return NextResponse.json(
        {
          success: false,
          message: `API test failed: ${response.status} ${response.statusText}`,
          details: errorText,
        },
        { status: response.status },
      )
    }

    const data = await response.json()

    return NextResponse.json({
      success: true,
      message: "Gemini API connection successful",
      models: isOpenRouterKey ? data.data.map((m) => m.id) : data.models.map((m) => m.name),
    })
  } catch (error: any) {
    logger.error("Gemini API test error", {
      error: error.message,
      stack: error.stack,
    })

    return NextResponse.json(
      {
        success: false,
        message: `Error testing Gemini API: ${error.message}`,
        error: error.stack,
      },
      { status: 500 },
    )
  }
}
