import { type NextRequest, NextResponse } from "next/server"
import { generateChatCompletion } from "@/lib/deepseek-client"
import { logger } from "@/lib/api/logger"
import { getCurrentUser } from "@/lib/auth-service"

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { messages, model, temperature, max_tokens, top_p, stop } = body

    // Validate required fields
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: "Messages are required and must be an array" }, { status: 400 })
    }

    // Log the request
    logger.info("DeepSeek API request", {
      userId: user.id,
      model: model || "deepseek-r1",
      messageCount: messages.length,
    })

    // Generate completion
    const response = await generateChatCompletion({
      messages,
      model: model || "deepseek-r1",
      temperature,
      max_tokens,
      top_p,
      stop,
    })

    // Return the response
    return NextResponse.json(response)
  } catch (error: any) {
    // Log the error
    logger.error("DeepSeek API error", { error: error.message, stack: error.stack })

    // Return error response
    return NextResponse.json(
      { error: error.message || "Failed to generate completion" },
      { status: error.status || 500 },
    )
  }
}
