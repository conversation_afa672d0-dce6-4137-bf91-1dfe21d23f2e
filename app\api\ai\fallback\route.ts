import { NextResponse } from "next/server"
import { logInfo, logError } from "@/lib/simple-logger"

export async function POST(request: Request) {
  try {
    // Get request data
    const { messages } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: "Valid messages array is required" }, { status: 400 })
    }

    // Try Gemini first
    if (process.env.GEMINI_API_KEY) {
      try {
        const geminiResponse = await fetch("/api/ai/gemini/direct", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ messages }),
        })

        if (geminiResponse.ok) {
          const data = await geminiResponse.json()
          return NextResponse.json({
            content: data.content,
            model: "gemini-2.0",
            status: "success",
          })
        }
      } catch (error) {
        logError("Gemini fallback failed", { error })
        // Continue to next fallback
      }
    }

    // If we get here, all API attempts failed - use local fallback
    logInfo("Using local fallback response")

    // Get last user message
    const userMessage = messages.filter((m) => m.role === "user").pop()?.content || ""

    let fallbackContent = "I'm here to chat and help you reflect on your journal entries."

    if (userMessage.includes("?")) {
      fallbackContent =
        "That's an interesting question. I'd be happy to discuss this topic once our AI service is back online."
    } else if (userMessage.toLowerCase().includes("hello") || userMessage.toLowerCase().includes("hi")) {
      fallbackContent =
        "Hello! I'm your ClarityLog assistant. I'm currently operating in offline mode, but I'm still here to chat."
    } else if (userMessage.length > 50) {
      fallbackContent =
        "Thank you for sharing your thoughts. I'm currently in offline mode with limited capabilities, but I appreciate your detailed message."
    }

    return NextResponse.json({
      content: `${fallbackContent} How can I assist you today?`,
      model: "offline",
      status: "fallback",
    })
  } catch (error: any) {
    logError("Fallback endpoint error", { error: error.message, stack: error.stack })

    return NextResponse.json({
      content: "I'm sorry, I'm having trouble responding right now. Please try again later.",
      model: "error",
      status: "error",
    })
  }
}
