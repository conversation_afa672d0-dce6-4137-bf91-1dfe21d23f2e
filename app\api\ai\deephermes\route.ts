import { NextResponse } from "next/server"
import { generateDeepHermesCompletion } from "@/lib/deephermes-client"
import { logger } from "@/lib/api/logger"

export async function POST(request: Request) {
  try {
    const { messages, temperature, max_tokens } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: "Invalid messages format" }, { status: 400 })
    }

    logger.info("DeepHermes API request", { messageCount: messages.length })

    const response = await generateDeepHermesCompletion(messages, {
      temperature: temperature || 0.7,
      max_tokens: max_tokens || 1000,
    })

    return NextResponse.json({
      content: response.choices[0].message.content,
      model: "deephermes-3-mistral-24b",
    })
  } catch (error) {
    logger.error("DeepHermes API error:", error)
    return NextResponse.json(
      {
        error: `An error occurred: ${error instanceof Error ? error.message : String(error)}`,
        model: "deephermes-3-mistral-24b",
      },
      { status: 500 },
    )
  }
}
