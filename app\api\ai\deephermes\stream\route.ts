import { NextResponse } from "next/server"
import { streamDeepHermesCompletion } from "@/lib/deephermes-client"
import { logger } from "@/lib/api/logger"

export const runtime = "nodejs"

export async function POST(request: Request) {
  try {
    const { messages, temperature, max_tokens } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: "Invalid messages format" }, { status: 400 })
    }

    logger.info("DeepHermes streaming API request", { messageCount: messages.length })

    const stream = await streamDeepHermesCompletion(messages, {
      temperature: temperature || 0.7,
      max_tokens: max_tokens || 1000,
    })

    // Create a readable stream from the OpenAI stream
    const textEncoder = new TextEncoder()
    const readable = new ReadableStream({
      async start(controller) {
        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content || ""
          if (content) {
            const data = {
              content,
              model: "deephermes-3-mistral-24b",
            }
            controller.enqueue(textEncoder.encode(`data: ${JSON.stringify(data)}\n\n`))
          }
        }
        controller.enqueue(textEncoder.encode("data: [DONE]\n\n"))
        controller.close()
      },
      cancel() {
        // Stream was canceled by the client
      },
    })

    return new Response(readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    })
  } catch (error) {
    logger.error("DeepHermes streaming API error:", error)

    // Return error as SSE
    const textEncoder = new TextEncoder()
    const readable = new ReadableStream({
      start(controller) {
        const data = {
          error: `An error occurred: ${error instanceof Error ? error.message : String(error)}`,
          model: "deephermes-3-mistral-24b",
        }
        controller.enqueue(textEncoder.encode(`data: ${JSON.stringify(data)}\n\n`))
        controller.enqueue(textEncoder.encode("data: [DONE]\n\n"))
        controller.close()
      },
    })

    return new Response(readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    })
  }
}
