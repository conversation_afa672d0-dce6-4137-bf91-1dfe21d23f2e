import { createDeepSeekR1Client } from "@/lib/deepseek-r1-client"
import { logError } from "@/lib/simple-logger"

export async function POST(request: Request) {
  try {
    // Parse request body
    const { messages, max_tokens = 500, temperature = 0.7 } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return new Response(JSON.stringify({ error: "Valid messages array is required" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
        },
      })
    }

    // Check if DeepSeek R1 API key is available
    if (!process.env.DEEPSEEK_R1_API_KEY) {
      return new Response(JSON.stringify({ error: "DeepSeek R1 API key is not configured" }), {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      })
    }

    // Create DeepSeek R1 client
    const client = createDeepSeekR1Client()

    // Test connection first
    const connectionTest = await client.testConnection()
    if (!connectionTest.success) {
      logError("DeepSeek R1 connection test failed", { error: connectionTest.error })
      return new Response(JSON.stringify({ error: `DeepSeek R1 connection failed: ${connectionTest.error}` }), {
        status: 502,
        headers: {
          "Content-Type": "application/json",
        },
      })
    }

    // Get the streaming response
    const stream = await client.createStreamingChatCompletion(messages, {
      max_tokens,
      temperature,
    })

    // Create a TransformStream to process the DeepSeek stream format
    const transformStream = new TransformStream({
      async transform(chunk, controller) {
        const decoder = new TextDecoder()
        const text = decoder.decode(chunk)

        // Process the chunk (DeepSeek format)
        const lines = text.split("\n")
        for (const line of lines) {
          if (line.startsWith("data: ") && line !== "data: [DONE]") {
            try {
              const data = JSON.parse(line.substring(6))
              if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                // Format in a way the client expects
                const formattedData = {
                  content: data.choices[0].delta.content,
                  model: "deepseek-r1",
                }
                controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(formattedData)}\n\n`))
              }
            } catch (e) {
              // Ignore parsing errors
            }
          } else if (line === "data: [DONE]") {
            controller.enqueue(new TextEncoder().encode("data: [DONE]\n\n"))
          }
        }
      },
    })

    // Pipe the stream through the transformer
    const responseStream = stream.pipeThrough(transformStream)

    // Return the streaming response
    return new Response(responseStream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    })
  } catch (error) {
    logError("DeepSeek R1 streaming API error:", error)

    return new Response(
      JSON.stringify({
        error: error.message || "Unknown error",
        model: "deepseek-r1",
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  }
}
