import { NextResponse } from "next/server"
import { auth as clerkAuth } from "@clerk/nextjs/server"
import { logger } from "@/lib/logger"
import { getUserPersonalDataContext } from "@/lib/services/ai-context-service"
import { checkRateLimit } from "@/lib/api/rate-limiter"
import { securityAuditService } from "@/lib/services/security-audit-service"

export async function POST(request: Request) {
  try {
    // Check authentication
    const { userId } = await clerkAuth()
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check rate limit
    const rateLimitResult = await checkRateLimit(request, "ai-chat", userId, {
      maxRequests: 30, // 30 requests per hour per user
      windowMs: 60 * 60 * 1000
    })
    if (!rateLimitResult.allowed) {
      // Log rate limit violation
      await securityAuditService.logRateLimitViolation(
        userId,
        "ai-chat",
        request.headers.get("x-forwarded-for") || "unknown"
      )

      return NextResponse.json(
        {
          error: "Rate limit exceeded",
          resetAt: rateLimitResult.resetAt.toISOString(),
        },
        { status: 429 }
      )
    }

    // Parse request body
    const {
      messages,
      model = "openrouter/deepseek-r1",
      temperature = 0.7,
      max_tokens = 1000,
      includePersonalData = true
    } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: "Messages array is required" }, { status: 400 })
    }

    // Validate message content
    if (messages.length === 0) {
      return NextResponse.json({ error: "At least one message is required" }, { status: 400 })
    }

    // Validate message structure and content length
    for (const message of messages) {
      if (!message.role || !message.content) {
        return NextResponse.json({ error: "Invalid message format" }, { status: 400 })
      }
      if (typeof message.content !== 'string' || message.content.length > 10000) {
        return NextResponse.json({ error: "Message content too long" }, { status: 400 })
      }
    }

    // Check if OpenRouter API key is available
    const apiKey = process.env.OPENROUTER_API_KEY
    if (!apiKey) {
      return NextResponse.json({ error: "OpenRouter API key not configured" }, { status: 500 })
    }

    // Use the model specified in the request, fallback to default free model
    const openrouterModel = model || "deepseek/deepseek-r1-0528-qwen3-8b:free"

    logger.info("Making OpenRouter API call", {
      model: openrouterModel,
      messageCount: messages.length,
      userId: userId.substring(0, 8) + "...", // Partial user ID for privacy
      includePersonalData
    })

    // Get user's personal data context if requested
    let enhancedMessages = messages
    if (includePersonalData) {
      try {
        // Log personal data access
        await securityAuditService.logPersonalDataAccess(
          userId,
          "/api/ai/chat-openrouter",
          ["journal_entries", "goals", "compass_assessments", "analytics"],
          request.headers.get("x-forwarded-for") || "unknown"
        )

        const personalContext = await getUserPersonalDataContext(userId)

        // Create a system message with user context
        const systemMessage = {
          role: "system",
          content: `You are an AI assistant for the Reflect journal app. You have access to the user's personal data to provide contextual, personalized responses. Here's what you know about the user:

${personalContext.summary}

IMPORTANT GUIDELINES:
- Reference the user's actual data when relevant to provide personalized insights
- Suggest goals based on journal mood patterns and existing goals
- Reference C.O.M.P.A.S.S. framework progress when giving problem-solving advice
- Use analytics data to provide insights about patterns and trends
- Be supportive and encouraging based on their progress
- Maintain privacy - never share specific details with others
- If you reference their data, be natural about it (e.g., "I noticed in your recent journal entries...")

Recent Context:
${personalContext.recentActivity}

Current Goals Status:
${personalContext.goalsStatus}

Mood Patterns:
${personalContext.moodInsights}

C.O.M.P.A.S.S. Progress:
${personalContext.compassProgress}`
        }

        // Add system message at the beginning
        enhancedMessages = [systemMessage, ...messages]

        logger.info("Enhanced messages with personal context", {
          userId,
          contextLength: personalContext.summary.length
        })
      } catch (error) {
        logger.error("Failed to get personal context", { userId, error })
        // Continue without personal context if there's an error
      }
    }

    // Make request to OpenRouter using the exact format you provided
    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "HTTP-Referer": process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3002",
        "X-Title": "Reflect Journal App",
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        model: openrouterModel,
        messages: enhancedMessages,
        temperature,
        max_tokens
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      logger.error("OpenRouter API error", {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })

      // Log failed API call
      await securityAuditService.logAPICall(
        userId,
        "/api/ai/chat-openrouter",
        false,
        request.headers.get("x-forwarded-for") || "unknown",
        `OpenRouter API error: ${response.status} ${response.statusText}`
      )

      return NextResponse.json(
        { error: `OpenRouter API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      )
    }

    const data = await response.json()

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      logger.error("Invalid OpenRouter response format", { data })
      return NextResponse.json({ error: "Invalid response from AI service" }, { status: 500 })
    }

    const assistantMessage = data.choices[0].message.content

    logger.info("OpenRouter API call successful", {
      model: openrouterModel,
      responseLength: assistantMessage?.length || 0,
      usage: data.usage
    })

    // Log successful API call
    await securityAuditService.logAPICall(
      userId,
      "/api/ai/chat-openrouter",
      true,
      request.headers.get("x-forwarded-for") || "unknown"
    )

    return NextResponse.json({
      content: assistantMessage,
      model: openrouterModel,
      usage: data.usage
    })

  } catch (error: any) {
    logger.error("Chat API error:", error)

    // Log failed API call
    await securityAuditService.logAPICall(
      userId || "unknown",
      "/api/ai/chat-openrouter",
      false,
      request.headers.get("x-forwarded-for") || "unknown",
      error.message || "Internal server error"
    )

    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}
