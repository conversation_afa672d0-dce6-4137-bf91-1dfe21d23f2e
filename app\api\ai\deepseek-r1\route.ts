import { NextResponse } from "next/server"
import { createDeepSeekR1Client } from "@/lib/deepseek-r1-client"
import { logError } from "@/lib/simple-logger"

export async function POST(request: Request) {
  try {
    // Parse request body
    const { messages, max_tokens = 500, temperature = 0.7 } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: "Valid messages array is required" }, { status: 400 })
    }

    // Check if DeepSeek R1 API key is available
    if (!process.env.DEEPSEEK_R1_API_KEY) {
      return NextResponse.json({ error: "DeepSeek R1 API key is not configured" }, { status: 500 })
    }

    // Create DeepSeek R1 client
    const client = createDeepSeekR1Client()

    // Test connection first
    const connectionTest = await client.testConnection()
    if (!connectionTest.success) {
      logError("DeepSeek R1 connection test failed", { error: connectionTest.error })
      return NextResponse.json({ error: `DeepSeek R1 connection failed: ${connectionTest.error}` }, { status: 502 })
    }

    // Generate completion
    const result = await client.createChatCompletion(messages, {
      max_tokens,
      temperature,
      timeout: 20000, // 20 second timeout
    })

    return NextResponse.json({
      content: result.content,
      model: result.model,
      usage: result.usage,
    })
  } catch (error) {
    logError("DeepSeek R1 API error:", error)

    return NextResponse.json(
      {
        error: error.message || "Unknown error",
        model: "deepseek-r1",
      },
      { status: 500 },
    )
  }
}
