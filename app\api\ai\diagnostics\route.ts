import { NextResponse } from "next/server"
import { runAIDiagnostics } from "@/lib/ai-diagnostics"
import { getCurrentUser } from "@/lib/auth-service"
import { logger } from "@/lib/api/logger"
import { checkRateLimit } from "@/lib/api/rate-limiter"

export async function GET(request: Request) {
  try {
    // Check authentication
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    // Check rate limit for diagnostics
    const rateLimitResult = await checkRateLimit(request, "ai-diagnostics", user.id, {
      maxRequests: 10, // 10 diagnostic requests per hour per user
      windowMs: 60 * 60 * 1000
    })
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: "Rate limit exceeded",
          resetAt: rateLimitResult.resetAt.toISOString(),
        },
        { status: 429 }
      )
    }

    // Get query parameters
    const url = new URL(request.url)
    const service = url.searchParams.get("service") || "all"
    const verbose = url.searchParams.get("verbose") === "true"

    // Run diagnostics for the requested service(s)
    if (service === "all") {
      // Run diagnostics for all configured services
      const services = ["gemini", "deepseek-r1", "deepseek-v3"]
      const results = await Promise.all(
        services.map(async (svc) => {
          try {
            return await runAIDiagnostics({
              service: svc as any,
              verbose,
            })
          } catch (error) {
            logger.error(`Error running diagnostics for ${svc}`, { error })
            return {
              success: false,
              message: `Diagnostic failed for ${svc}`,
              details: error.message,
              timestamp: new Date().toISOString(),
              service: svc,
            }
          }
        }),
      )

      return NextResponse.json({
        results,
        timestamp: new Date().toISOString(),
        user: user.id,
      })
    } else {
      // Run diagnostics for a specific service
      const result = await runAIDiagnostics({
        service: service as any,
        verbose,
      })

      return NextResponse.json(result)
    }
  } catch (error) {
    logger.error("Error in diagnostics API", { error })

    return NextResponse.json(
      {
        error: "Failed to run diagnostics",
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
