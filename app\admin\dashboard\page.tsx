"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { createClient } from "@/lib/supabase/client"

interface ApiStats {
  totalRequests: number
  successRate: number
  avgProcessingTime: number
  requestsByEndpoint: Record<string, number>
  errorRate: number
  requestsLast24Hours: number
}

export default function ApiDashboardPage() {
  const [stats, setStats] = useState<ApiStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true)
      setError(null)

      try {
        const supabase = createClient()

        // Get total requests
        const { count: totalRequests, error: countError } = await supabase
          .from("api_logs")
          .select("*", { count: "exact", head: true })

        if (countError) throw countError

        // Get successful requests
        const { count: successfulRequests, error: successError } = await supabase
          .from("api_logs")
          .select("*", { count: "exact", head: true })
          .lt("response_status", 400)

        if (successError) throw successError

        // Get average processing time
        const { data: processingTimeData, error: timeError } = await supabase
          .from("api_logs")
          .select("processing_time_ms")

        if (timeError) throw timeError

        const avgProcessingTime =
          processingTimeData.reduce((sum, log) => sum + log.processing_time_ms, 0) / (processingTimeData.length || 1)

        // Get requests by endpoint
        const { data: endpointData, error: endpointError } = await supabase.from("api_logs").select("endpoint")

        if (endpointError) throw endpointError

        const requestsByEndpoint: Record<string, number> = {}
        endpointData.forEach((log) => {
          requestsByEndpoint[log.endpoint] = (requestsByEndpoint[log.endpoint] || 0) + 1
        })

        // Get requests in last 24 hours
        const oneDayAgo = new Date()
        oneDayAgo.setDate(oneDayAgo.getDate() - 1)

        const { count: recentRequests, error: recentError } = await supabase
          .from("api_logs")
          .select("*", { count: "exact", head: true })
          .gte("created_at", oneDayAgo.toISOString())

        if (recentError) throw recentError

        setStats({
          totalRequests: totalRequests || 0,
          successRate: totalRequests ? ((successfulRequests || 0) / totalRequests) * 100 : 100,
          avgProcessingTime,
          requestsByEndpoint,
          errorRate: totalRequests ? 100 - ((successfulRequests || 0) / totalRequests) * 100 : 0,
          requestsLast24Hours: recentRequests || 0,
        })
      } catch (err: any) {
        setError(err.message || "Failed to fetch API statistics")
        console.error("Error fetching API statistics:", err)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">API Usage Dashboard</h1>

      {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{error}</div>}

      {loading ? (
        <div className="text-center py-8">Loading statistics...</div>
      ) : stats ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Total Requests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold">{stats.totalRequests}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Success Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold text-green-500">{stats.successRate.toFixed(1)}%</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Error Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold text-red-500">{stats.errorRate.toFixed(1)}%</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Avg. Processing Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold">{stats.avgProcessingTime.toFixed(0)}ms</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Requests (Last 24h)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold">{stats.requestsLast24Hours}</div>
            </CardContent>
          </Card>

          <Card className="md:col-span-2 lg:col-span-3">
            <CardHeader>
              <CardTitle>Requests by Endpoint</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(stats.requestsByEndpoint).map(([endpoint, count]) => (
                  <div key={endpoint} className="flex justify-between items-center border-b pb-2">
                    <span className="font-medium">{endpoint}</span>
                    <span className="text-muted-foreground">{count} requests</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="text-center py-8">No statistics available</div>
      )}
    </div>
  )
}
