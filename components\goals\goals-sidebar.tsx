"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { 
  Target, 
  Heart, 
  Briefcase, 
  BookOpen, 
  DollarSign, 
  User, 
  Home, 
  Palette,
  Filter,
  Calendar,
  TrendingUp,
  CheckCircle2,
  Clock,
  Archive,
  Star,
  Award
} from "lucide-react"

interface Goal {
  id: string
  title: string
  category: string
  priority: "low" | "medium" | "high" | "critical"
  progress: number
  targetDate?: string
  archived?: boolean
}

interface GoalsSidebarProps {
  goals: Goal[]
  selectedCategory: string
  selectedPriority: string
  onCategoryChange: (category: string) => void
  onPriorityChange: (priority: string) => void
  className?: string
}

const CATEGORIES = [
  { id: "all", name: "All Categories", icon: Target, count: 0 },
  { id: "health", name: "Health & Fitness", icon: <PERSON>, count: 0 },
  { id: "career", name: "<PERSON>", icon: Briefcase, count: 0 },
  { id: "learning", name: "<PERSON>", icon: BookOpen, count: 0 },
  { id: "finance", name: "Finance", icon: DollarSign, count: 0 },
  { id: "personal", name: "Personal", icon: User, count: 0 },
  { id: "home", name: "Home & Family", icon: Home, count: 0 },
  { id: "creative", name: "Creative", icon: Palette, count: 0 }
]

const PRIORITIES = [
  { id: "all", name: "All Priorities", color: "bg-gray-100 text-gray-700", count: 0 },
  { id: "low", name: "Low Priority", color: "bg-green-100 text-green-700", count: 0 },
  { id: "medium", name: "Medium Priority", color: "bg-blue-100 text-blue-700", count: 0 },
  { id: "high", name: "High Priority", color: "bg-orange-100 text-orange-700", count: 0 },
  { id: "critical", name: "Critical", color: "bg-red-100 text-red-700", count: 0 }
]

export function GoalsSidebar({ 
  goals, 
  selectedCategory, 
  selectedPriority, 
  onCategoryChange, 
  onPriorityChange,
  className = "" 
}: GoalsSidebarProps) {
  
  // Calculate counts for categories and priorities
  const categoriesWithCounts = CATEGORIES.map(category => ({
    ...category,
    count: category.id === "all" ? goals.length : goals.filter(goal => goal.category === category.id).length
  }))

  const prioritiesWithCounts = PRIORITIES.map(priority => ({
    ...priority,
    count: priority.id === "all" ? goals.length : goals.filter(goal => goal.priority === priority.id).length
  }))

  // Calculate stats
  const stats = {
    total: goals.length,
    completed: goals.filter(goal => goal.progress === 100).length,
    inProgress: goals.filter(goal => goal.progress > 0 && goal.progress < 100).length,
    overdue: goals.filter(goal => {
      if (!goal.targetDate) return false
      const targetDate = new Date(goal.targetDate)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      return targetDate < today && goal.progress < 100
    }).length
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Quick Stats */}
      <Card className="appointy-card">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-purple-600" />
            Quick Stats
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{stats.total}</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Total</div>
            </div>
            <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.completed}</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Completed</div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{stats.inProgress}</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">In Progress</div>
            </div>
            <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">{stats.overdue}</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Overdue</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Categories Filter */}
      <Card className="appointy-card">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Filter className="h-5 w-5 text-purple-600" />
            Categories
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {categoriesWithCounts.map((category) => {
            const IconComponent = category.icon
            const isSelected = selectedCategory === category.id
            
            return (
              <Button
                key={category.id}
                variant="ghost"
                onClick={() => onCategoryChange(category.id)}
                className={cn(
                  "w-full justify-between h-auto p-3 text-left",
                  isSelected 
                    ? "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300" 
                    : "hover:bg-gray-100 dark:hover:bg-gray-700"
                )}
              >
                <div className="flex items-center gap-3">
                  <IconComponent className="h-4 w-4" />
                  <span className="font-medium">{category.name}</span>
                </div>
                <Badge variant="outline" className="text-xs">
                  {category.count}
                </Badge>
              </Button>
            )
          })}
        </CardContent>
      </Card>

      {/* Priority Filter */}
      <Card className="appointy-card">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Star className="h-5 w-5 text-purple-600" />
            Priority
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {prioritiesWithCounts.map((priority) => {
            const isSelected = selectedPriority === priority.id
            
            return (
              <Button
                key={priority.id}
                variant="ghost"
                onClick={() => onPriorityChange(priority.id)}
                className={cn(
                  "w-full justify-between h-auto p-3 text-left",
                  isSelected 
                    ? "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300" 
                    : "hover:bg-gray-100 dark:hover:bg-gray-700"
                )}
              >
                <div className="flex items-center gap-3">
                  <div className={cn("w-3 h-3 rounded-full", priority.color.split(' ')[0])} />
                  <span className="font-medium">{priority.name}</span>
                </div>
                <Badge variant="outline" className="text-xs">
                  {priority.count}
                </Badge>
              </Button>
            )
          })}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="appointy-card">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Award className="h-5 w-5 text-purple-600" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button 
            variant="outline" 
            className="w-full justify-start appointy-button-secondary"
          >
            <Calendar className="h-4 w-4 mr-2" />
            View Calendar
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full justify-start appointy-button-secondary"
          >
            <Archive className="h-4 w-4 mr-2" />
            Archived Goals
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full justify-start appointy-button-secondary"
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Analytics
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
