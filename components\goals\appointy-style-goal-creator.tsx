"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, <PERSON>alogT<PERSON>le, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { 
  Plus, Target, Calendar, Clock, User, Users, Star, Trophy, 
  Heart, Briefcase, BookOpen, DollarSign, Home, Palette,
  X, ChevronDown, Zap
} from "lucide-react"
import { cn } from "@/lib/utils"
import { createGoal } from "@/lib/local-storage/goals-storage"
import { useToast } from "@/hooks/use-toast"
import { useGamificationStore } from "@/lib/stores/gamification-store"

interface AppointyGoalCreatorProps {
  onGoalCreated?: () => void
  trigger?: React.ReactNode
}

interface GoalFormData {
  title: string
  description: string
  category: string
  priority: "low" | "medium" | "high" | "critical"
  complexity: "simple" | "moderate" | "complex" | "expert"
  targetDate: string
  estimatedHours: number
  isCollaborative: boolean
  tags: string[]
}

const CATEGORIES = [
  { id: "health", name: "Health & Fitness", icon: Heart, color: "text-red-500", bg: "bg-red-50", border: "border-red-200" },
  { id: "career", name: "Career", icon: Briefcase, color: "text-blue-500", bg: "bg-blue-50", border: "border-blue-200" },
  { id: "learning", name: "Learning", icon: BookOpen, color: "text-green-500", bg: "bg-green-50", border: "border-green-200" },
  { id: "finance", name: "Finance", icon: DollarSign, color: "text-yellow-500", bg: "bg-yellow-50", border: "border-yellow-200" },
  { id: "personal", name: "Personal", icon: User, color: "text-purple-500", bg: "bg-purple-50", border: "border-purple-200" },
  { id: "home", name: "Home & Family", icon: Home, color: "text-pink-500", bg: "bg-pink-50", border: "border-pink-200" },
  { id: "creative", name: "Creative", icon: Palette, color: "text-indigo-500", bg: "bg-indigo-50", border: "border-indigo-200" }
]

const PRIORITY_OPTIONS = [
  { value: "low", label: "Low Priority", color: "bg-gray-100 text-gray-700" },
  { value: "medium", label: "Medium Priority", color: "bg-blue-100 text-blue-700" },
  { value: "high", label: "High Priority", color: "bg-orange-100 text-orange-700" },
  { value: "critical", label: "Critical", color: "bg-red-100 text-red-700" }
]

export function AppointyStyleGoalCreator({ onGoalCreated, trigger }: AppointyGoalCreatorProps) {
  const [open, setOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()
  const { addXp, incrementGoalsCreated } = useGamificationStore()

  const [formData, setFormData] = useState<GoalFormData>({
    title: "",
    description: "",
    category: "personal",
    priority: "medium",
    complexity: "moderate",
    targetDate: "",
    estimatedHours: 10,
    isCollaborative: false,
    tags: []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const updateFormData = (field: keyof GoalFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = "Goal title is required"
    }

    if (formData.targetDate) {
      const targetDate = new Date(formData.targetDate)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (targetDate <= today) {
        newErrors.targetDate = "Target date must be in the future"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const calculateXpReward = () => {
    const baseXp = 50
    const priorityMultiplier = {
      low: 1.0, medium: 1.5, high: 2.0, critical: 2.5
    }[formData.priority]
    const complexityBonus = {
      simple: 0, moderate: 10, complex: 25, expert: 50
    }[formData.complexity]
    const collaborationBonus = formData.isCollaborative ? 20 : 0

    return Math.round((baseXp + complexityBonus + collaborationBonus) * priorityMultiplier)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isSubmitting || !validateForm()) return

    setIsSubmitting(true)

    try {
      const newGoal = createGoal({
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        target_date: formData.targetDate || undefined,
        category: formData.category,
        priority: formData.priority,
        complexity: formData.complexity,
        estimated_hours: formData.estimatedHours,
        collaboration_type: formData.isCollaborative ? "team" : "solo"
      })

      const xpReward = calculateXpReward()
      addXp(xpReward, "goal_creation")
      incrementGoalsCreated(formData.category)

      resetForm()
      setOpen(false)

      toast({
        title: "Goal Created! 🎯",
        description: `"${formData.title}" created successfully with +${xpReward} XP`,
      })

      if (onGoalCreated) {
        onGoalCreated()
      }

    } catch (error) {
      console.error("Error creating goal:", error)
      toast({
        title: "Error",
        description: "Failed to create goal. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      category: "personal",
      priority: "medium",
      complexity: "moderate",
      targetDate: "",
      estimatedHours: 10,
      isCollaborative: false,
      tags: []
    })
    setErrors({})
  }

  const selectedCategory = CATEGORIES.find(c => c.id === formData.category)

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      if (!newOpen) resetForm()
      setOpen(newOpen)
    }}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg shadow-sm">
            <Plus className="h-4 w-4 mr-2" />
            New Goal
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl">
        <DialogHeader className="pb-4 border-b border-gray-100 dark:border-gray-800">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <Target className="h-5 w-5 text-purple-600" />
              Create New Goal
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setOpen(false)}
              className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 pt-4">
          {/* Goal Name */}
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Goal Name
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => updateFormData("title", e.target.value)}
              placeholder="Enter goal title"
              className={cn(
                "border-gray-300 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500",
                errors.title && "border-red-500"
              )}
            />
            {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Date</Label>
              <Input
                type="date"
                value={formData.targetDate}
                onChange={(e) => updateFormData("targetDate", e.target.value)}
                className={cn(
                  "border-gray-300 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500",
                  errors.targetDate && "border-red-500"
                )}
              />
              {errors.targetDate && <p className="text-sm text-red-500">{errors.targetDate}</p>}
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Estimated Hours: {formData.estimatedHours}h
              </Label>
              <Input
                type="range"
                min="1"
                max="100"
                value={formData.estimatedHours}
                onChange={(e) => updateFormData("estimatedHours", parseInt(e.target.value))}
                className="w-full"
              />
            </div>
          </div>

          {/* Category Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Category</Label>
            <div className="grid grid-cols-2 gap-2">
              {CATEGORIES.map((category) => {
                const IconComponent = category.icon
                const isSelected = formData.category === category.id
                return (
                  <button
                    key={category.id}
                    type="button"
                    onClick={() => updateFormData("category", category.id)}
                    className={cn(
                      "p-3 rounded-lg border-2 transition-all text-left flex items-center gap-3",
                      isSelected
                        ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                        : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                    )}
                  >
                    <IconComponent className={`h-5 w-5 ${category.color}`} />
                    <span className="font-medium text-gray-900 dark:text-gray-100">{category.name}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Priority and Collaboration */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Priority</Label>
              <Select value={formData.priority} onValueChange={(value) => updateFormData("priority", value)}>
                <SelectTrigger className="border-gray-300 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PRIORITY_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <Badge className={option.color}>{option.label}</Badge>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Collaboration</Label>
              <div className="flex items-center justify-between p-3 border border-gray-300 dark:border-gray-600 rounded-lg">
                <div className="flex items-center gap-2">
                  {formData.isCollaborative ? <Users className="h-4 w-4" /> : <User className="h-4 w-4" />}
                  <span className="text-sm">{formData.isCollaborative ? "Team Goal" : "Solo Goal"}</span>
                </div>
                <Switch
                  checked={formData.isCollaborative}
                  onCheckedChange={(checked) => updateFormData("isCollaborative", checked)}
                />
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Description
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => updateFormData("description", e.target.value)}
              placeholder="Add description"
              rows={3}
              className="border-gray-300 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500 resize-none"
            />
          </div>

          {/* XP Preview */}
          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-600" />
                <span className="font-medium text-purple-900 dark:text-purple-100">XP Reward</span>
              </div>
              <Badge className="bg-purple-600 text-white">+{calculateXpReward()} XP</Badge>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t border-gray-100 dark:border-gray-800">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
            >
              {isSubmitting ? "Creating..." : "Save Goal"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
