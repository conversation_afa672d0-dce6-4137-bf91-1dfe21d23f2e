import { type NextRequest, NextResponse } from "next/server"
import {
  getShardingConfig,
  updateShardingConfig,
  addShard,
  updateShard,
  removeShard,
  setShardingEnabled,
  setShardingStrategy,
  setDefaultShard,
} from "@/lib/sharding/shard-config"
import { getReshardingUtility } from "@/lib/sharding/resharding-utility"
import { logger } from "@/lib/api/logger"

// Admin middleware would go here in a real application
async function verifyAdminAccess(req: NextRequest) {
  // In a real application, you would verify that the user is an admin
  // For now, we'll just check for an admin token in the headers
  const adminToken = req.headers.get("x-admin-token")

  if (!adminToken || adminToken !== process.env.ADMIN_API_TOKEN) {
    return false
  }

  return true
}

export async function GET(req: NextRequest) {
  // Verify admin access
  const isAdmin = await verifyAdminAccess(req)
  if (!isAdmin) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    // Get current sharding configuration
    const config = getShardingConfig()

    return NextResponse.json({ config })
  } catch (error: any) {
    logger.error("admin/sharding/get", {
      error: error.message,
    })

    return NextResponse.json({ error: "Failed to get sharding configuration" }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  // Verify admin access
  const isAdmin = await verifyAdminAccess(req)
  if (!isAdmin) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await req.json()
    const { action } = body

    switch (action) {
      case "update_config":
        const { config } = body
        const updatedConfig = updateShardingConfig(config)
        return NextResponse.json({ config: updatedConfig })

      case "add_shard":
        const { shard } = body
        const configWithNewShard = addShard(shard)
        return NextResponse.json({ config: configWithNewShard })

      case "update_shard":
        const { shardId, updates } = body
        const configWithUpdatedShard = updateShard(shardId, updates)
        return NextResponse.json({ config: configWithUpdatedShard })

      case "remove_shard":
        const { shardIdToRemove } = body
        const configWithRemovedShard = removeShard(shardIdToRemove)
        return NextResponse.json({ config: configWithRemovedShard })

      case "enable_sharding":
        const { enabled } = body
        const configWithEnabledSetting = setShardingEnabled(enabled)
        return NextResponse.json({ config: configWithEnabledSetting })

      case "set_strategy":
        const { strategy } = body
        const configWithStrategy = setShardingStrategy(strategy)
        return NextResponse.json({ config: configWithStrategy })

      case "set_default_shard":
        const { defaultShardId } = body
        const configWithDefaultShard = setDefaultShard(defaultShardId)
        return NextResponse.json({ config: configWithDefaultShard })

      case "migrate_user":
        const { userId, sourceShardId, targetShardId } = body
        const reshardingUtility = getReshardingUtility()
        const migrationResult = await reshardingUtility.migrateUser(userId, sourceShardId, targetShardId)
        return NextResponse.json({ success: migrationResult })

      case "rebalance_shards":
        const { targetDistribution } = body
        const reshardingUtil = getReshardingUtility()
        const rebalanceResult = await reshardingUtil.rebalanceShards(new Map(Object.entries(targetDistribution)))
        return NextResponse.json({ success: rebalanceResult })

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 })
    }
  } catch (error: any) {
    logger.error("admin/sharding/post", {
      error: error.message,
    })

    return NextResponse.json({ error: "Failed to update sharding configuration" }, { status: 500 })
  }
}
