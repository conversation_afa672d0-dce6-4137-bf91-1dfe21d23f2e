import { NextResponse } from "next/server"
import { getCurrentUser } from "@/lib/auth-service"
import { logInfo, logError, logWarn } from "@/lib/simple-logger"

// Helper function to get available AI model with improved resilience
function getAvailableModel() {
  // Check for environment variables
  const hasGemini = <PERSON><PERSON><PERSON>(process.env.GEMINI_API_KEY)
  const hasDeepseekV3 = Boolean(process.env.DEEPSEEK_V3_API_KEY)
  const hasDeepseekR1 = <PERSON><PERSON><PERSON>(process.env.DEEPSEEK_R1_API_KEY)

  // Log available models
  logInfo("Available models", { hasGemini, hasDeepseekV3, hasDeepseekR1 })

  // Prioritize Gemini since it's been more reliable
  if (hasGemini) {
    return {
      type: "gemini",
      version: "2.0",
      key: process.env.GEMINI_API_KEY,
    }
  } else if (hasDeepseekV3) {
    return {
      type: "deepseek",
      version: "v3",
      key: process.env.DEEPSEEK_V3_API_KEY,
    }
  } else if (hasDeepseekR1) {
    return {
      type: "deepseek",
      version: "r1",
      key: process.env.DEEPSEEK_R1_API_KEY,
    }
  }

  // If no models are available, return a mock model
  // This allows the system to still function in a limited capacity
  return {
    type: "mock",
    version: "1.0",
    key: "mock-key",
  }
}

export async function POST(request: Request) {
  try {
    // Get current user (with fallback for development)
    let user
    try {
      user = await getCurrentUser()
    } catch (error) {
      logWarn("Auth error, using development user")
      user = { id: "dev-user-id", name: "Development User" }
    }

    // Parse request body
    const { messages, max_tokens = 300, temperature = 0.7 } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: "Valid messages array is required" }, { status: 400 })
    }

    // Log request
    logInfo("Fallback chat API request", {
      userId: user.id,
      messageCount: messages.length,
    })

    // Get available model
    const model = getAvailableModel()

    // Process request based on available model
    if (model.type === "deepseek") {
      return await handleDeepSeekRequest(messages, model, max_tokens, temperature)
    } else if (model.type === "gemini") {
      return await handleGeminiRequest(messages, model, max_tokens, temperature)
    } else {
      // Mock response for when no models are available
      return handleMockResponse(messages)
    }
  } catch (error: any) {
    logError("Fallback chat API error:", error)

    // Return a more helpful error message
    return NextResponse.json(
      {
        content: "I encountered a temporary issue processing your request. Please try again in a moment.",
        error: error.message || "Unknown error",
        model: "error",
      },
      { status: 500 },
    )
  }
}

async function handleDeepSeekRequest(messages, model, max_tokens, temperature) {
  const modelId = model.version === "v3" ? "deepseek-chat" : "deepseek-coder"

  try {
    logInfo("Calling DeepSeek API", { modelId })

    // Set timeout for the request
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

    try {
      const response = await fetch("https://api.deepseek.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${model.key}`,
        },
        body: JSON.stringify({
          model: modelId,
          messages,
          max_tokens,
          temperature,
        }),
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.status}`)
      }

      const data = await response.json()

      return NextResponse.json({
        content: data.choices[0].message.content,
        model: `deepseek-${model.version}`,
        usage: data.usage,
      })
    } finally {
      clearTimeout(timeoutId)
    }
  } catch (error) {
    logError("DeepSeek API error:", error)

    // Try to fall back to Gemini if available
    if (process.env.GEMINI_API_KEY) {
      logInfo("Falling back to Gemini after DeepSeek error")
      return handleGeminiRequest(
        messages,
        {
          type: "gemini",
          version: "2.0",
          key: process.env.GEMINI_API_KEY,
        },
        max_tokens,
        temperature,
      )
    }

    // If no fallback is available, return a mock response
    return handleMockResponse(messages)
  }
}

async function handleGeminiRequest(messages, model, max_tokens, temperature) {
  try {
    logInfo("Calling Gemini API")

    // Convert messages format from OpenAI to Gemini format
    const geminiMessages = messages.map((msg) => ({
      role: msg.role === "assistant" ? "model" : msg.role,
      parts: [{ text: msg.content }],
    }))

    // Set timeout for the request
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

    try {
      const response = await fetch(
        "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-goog-api-key": model.key,
          },
          body: JSON.stringify({
            contents: geminiMessages,
            generationConfig: {
              maxOutputTokens: max_tokens,
              temperature,
            },
          }),
          signal: controller.signal,
        },
      )

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`)
      }

      const data = await response.json()

      // Extract the response text from Gemini's response format
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text || "No response generated."

      return NextResponse.json({
        content,
        model: "gemini-2.0",
        usage: {
          total_tokens: data.usage?.totalTokens || 0,
        },
      })
    } finally {
      clearTimeout(timeoutId)
    }
  } catch (error) {
    logError("Gemini API error:", error)

    // Try to fall back to DeepSeek if available
    if (process.env.DEEPSEEK_V3_API_KEY) {
      logInfo("Falling back to DeepSeek V3 after Gemini error")
      return handleDeepSeekRequest(
        messages,
        {
          type: "deepseek",
          version: "v3",
          key: process.env.DEEPSEEK_V3_API_KEY,
        },
        max_tokens,
        temperature,
      )
    } else if (process.env.DEEPSEEK_R1_API_KEY) {
      logInfo("Falling back to DeepSeek R1 after Gemini error")
      return handleDeepSeekRequest(
        messages,
        {
          type: "deepseek",
          version: "r1",
          key: process.env.DEEPSEEK_R1_API_KEY,
        },
        max_tokens,
        temperature,
      )
    }

    // If no fallback is available, return a mock response
    return handleMockResponse(messages)
  }
}

// Improved mock response function that tries to be more contextually relevant
function handleMockResponse(messages) {
  logInfo("Using mock response")

  // Get the last user message
  const lastUserMessage = messages.filter((m) => m.role === "user").pop()?.content || ""

  // Generate a contextually appropriate response
  let responseContent = "I'm here to help with your journaling experience."

  // Check for common patterns in the message
  if (lastUserMessage.includes("?")) {
    if (lastUserMessage.toLowerCase().includes("how are you")) {
      responseContent =
        "I'm doing well, thank you for asking! How are you feeling today? Would you like to discuss anything specific about your journaling experience?"
    } else if (lastUserMessage.toLowerCase().includes("what can you do")) {
      responseContent =
        "I can help you with your journaling experience in several ways. I can provide writing prompts, discuss your entries, offer reflection questions, or just chat about your day. What would you like help with today?"
    } else {
      responseContent =
        "That's an interesting question. I'm currently operating with limited capabilities, but I'd be happy to discuss this topic with you. Could you share more about what you're interested in?"
    }
  } else if (lastUserMessage.toLowerCase().includes("hello") || lastUserMessage.toLowerCase().includes("hi")) {
    responseContent = "Hello! It's nice to chat with you. How can I assist with your journaling today?"
  } else if (lastUserMessage.length > 100) {
    responseContent =
      "Thank you for sharing that with me. I appreciate you taking the time to write such a detailed message. Would you like to explore any particular aspects of what you've shared?"
  }

  return NextResponse.json({
    content: responseContent,
    model: "offline-assistant",
    status: "mock",
  })
}
