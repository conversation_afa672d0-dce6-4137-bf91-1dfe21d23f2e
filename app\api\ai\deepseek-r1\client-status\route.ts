import { NextResponse } from "next/server"

/**
 * API route to check if DeepSeek R1 is available without exposing the API key to the client
 * This is a safer approach than using NEXT_PUBLIC_ environment variables for API keys
 */
export async function GET() {
  try {
    // Check if DeepSeek R1 API key is available
    const isAvailable = Boolean(process.env.DEEPSEEK_R1_API_KEY)

    // Return status without exposing the actual key
    return NextResponse.json({
      available: isAvailable,
      service: "deepseek-r1",
    })
  } catch (error) {
    console.error("Error checking DeepSeek R1 availability:", error)
    return NextResponse.json(
      {
        available: false,
        error: "Failed to check DeepSeek R1 availability",
        service: "deepseek-r1",
      },
      { status: 500 },
    )
  }
}
