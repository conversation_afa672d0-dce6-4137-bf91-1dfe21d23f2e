"use client"
// @ts-nocheck

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"

import { useState, useEffect, useRef, useCallback } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { GoalList } from "@/components/goals/goal-list"
import { registerShortcut } from "@/lib/keyboard-shortcuts"
import { Button } from "@/components/ui/button"
import { CreateGoalButton } from "@/components/goals/create-goal-button"
import { EnhancedGoalCreator } from "@/components/goals/enhanced-goal-creator"
import { AppointyStyleGoalCreator } from "@/components/goals/appointy-style-goal-creator"
import { DailyChallenges } from "@/components/gamification/daily-challenges"
import { Archive, Target, Award, Trophy, Crown, Zap, CheckCircle2, TrendingUp, Clock, Plus, Filter, Search, Calendar, Grid, List } from "lucide-react"
import Link from "next/link"
import { GoalAnalytics } from "@/components/goals/goal-analytics"
import { useToast } from "@/hooks/use-toast"
import { initializeGoalsData } from "@/lib/local-storage/goals-initializer"
import { useGoalStore } from "@/lib/stores/goal-store"
import { motion } from "framer-motion"
import { debounce } from "@/lib/utils/debounce"

// Update the imports to include our gamification components and store
import { useGamificationStore } from "@/lib/stores/gamification-store"
import { XpProgressBar } from "@/components/gamification/xp-progress-bar"
import { StreakIndicator } from "@/components/gamification/streak-indicator"
import { GamificationProfile } from "@/components/gamification/gamification-profile"
import { LevelUpCelebration } from "@/components/gamification/level-up-celebration"
import { BadgeEarned } from "@/components/gamification/badge-earned"
import { GameStyleGoalCard } from "@/components/gamification/game-style-goal-card"
import { MinimalistGoalCard } from "@/components/goals/minimalist-goal-card"
import { GoalsCalendarView } from "@/components/goals/goals-calendar-view"
import { GoalsSidebar } from "@/components/goals/goals-sidebar"

export default function GoalsPage() {
  const [loading, setLoading] = useState(true)
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const [viewMode, setViewMode] = useState<"grid" | "list" | "calendar">("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedPriority, setSelectedPriority] = useState<string>("all")
  const searchInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  // Add state for level-up and badge celebrations
  const [showLevelUp, setShowLevelUp] = useState(false)
  const [showBadge, setShowBadge] = useState(false)
  const [newLevel, setNewLevel] = useState(1)
  const [earnedBadge, setEarnedBadge] = useState<any>(null)

  // Use our goal store
  const { goals, loadGoals } = useGoalStore()

  // Use the gamification store with enhanced features
  const {
    level,
    totalXp,
    levelProgress,
    currentStreak,
    longestStreak,
    recentlyEarnedBadges,
    clearRecentBadges,
    levelInfo,
    prestigeLevel,
    currentXpMultiplier,
    earnedBadges,
    nextBadges,
    updateDailyActivity
  } = useGamificationStore()

  const [goalStats, setGoalStats] = useState({
    total: 0,
    completed: 0,
    inProgress: 0,
    notStarted: 0,
    overdue: 0,
  })

  // Add state for profile dialog
  const [showProfile, setShowProfile] = useState(false)

  // Calculate goal statistics
  const calculateGoalStats = useCallback(() => {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const stats = {
        total: goals.length,
        completed: goals.filter((g) => g.progress === 100).length,
        inProgress: goals.filter((g) => g.progress > 0 && g.progress < 100).length,
        notStarted: goals.filter((g) => g.progress === 0).length,
        overdue: goals.filter((g) => {
          if (!g.targetDate) return false
          const targetDate = new Date(g.targetDate)
          targetDate.setHours(0, 0, 0, 0)
          return targetDate < today && g.progress < 100
        }).length,
      }

      setGoalStats(stats)
    } catch (error) {
      console.error("Error calculating goal stats:", error)
      toast({
        title: "Error",
        description: "Failed to load goal statistics. Please refresh the page.",
        variant: "destructive",
      })
    }
  }, [goals, toast])

  // Initialize goals data and load goals
  useEffect(() => {
    const init = async () => {
      try {
        setLoading(true)
        initializeGoalsData()
        updateDailyActivity()
        await loadGoals()
        setLoading(false)
      } catch (error) {
        console.error('Error initializing goals:', error)
        toast({
          title: 'Error',
          description: 'Failed to load goals. Please refresh the page.',
          variant: 'destructive',
        })
        setLoading(false)
      }
    }
    init()
  }, [loadGoals, toast, updateDailyActivity])

  // Calculate stats when goals change
  useEffect(() => {
    calculateGoalStats()
  }, [goals, calculateGoalStats])

  // Register keyboard shortcuts
  useEffect(() => {
    const searchShortcut = registerShortcut("search", () => {
      if (searchInputRef.current) {
        searchInputRef.current.focus()
      }
    })

    return () => {
      searchShortcut()
    }
  }, [])

  // Simplified event handling for goal/task updates
  useEffect(() => {
    const handleUpdate = () => {
      loadGoals().then(() => {
        calculateGoalStats()
        setRefreshKey((prev) => prev + 1)
      })
    }

    window.addEventListener("goal-task-update", handleUpdate)
    window.addEventListener("storageUpdate", handleUpdate)
    window.addEventListener("task-status-changed", handleUpdate)

    return () => {
      window.removeEventListener("goal-task-update", handleUpdate)
      window.removeEventListener("storageUpdate", handleUpdate)
      window.removeEventListener("task-status-changed", handleUpdate)
    }
  }, [loadGoals, calculateGoalStats])



  // Check for recent badges on component mount
  useEffect(() => {
    if (recentlyEarnedBadges.length > 0) {
      setEarnedBadge(recentlyEarnedBadges[0])
      setShowBadge(true)
      clearRecentBadges()
    }
  }, [recentlyEarnedBadges, clearRecentBadges])

  // Function to dispatch task status change events
  const dispatchTaskStatusChangeEvent = (taskId, goalId, newStatus) => {
    window.dispatchEvent(
      new CustomEvent("task-status-changed", {
        detail: {
          taskId,
          goalId,
          status: newStatus,
          timestamp: Date.now(),
        },
      }),
    )
  }

  const handleGoalCreated = useCallback(() => {
    loadGoals().then(() => {
      calculateGoalStats()
      setRefreshKey((prev) => prev + 1)
      toast({
        title: "Goal Created",
        description: "Your new goal has been created successfully!",
      })
    })
  }, [loadGoals, calculateGoalStats, toast])

  const handleGoalUpdated = useCallback(() => {
    loadGoals().then(() => {
      calculateGoalStats()
      setRefreshKey((prev) => prev + 1)
    })
  }, [loadGoals, calculateGoalStats])

  const handleViewGoalDetails = useCallback((goalId: string) => {
    window.location.href = `/goals/view/${goalId}`
  }, [])

  // Add these CSS classes for animations
  const taskUpdatedPulseStyle = `
  @keyframes task-updated-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.03); box-shadow: 0 0 15px rgba(59, 130, 246, 0.5); }
    100% { transform: scale(1); }
  }
  .task-updated-pulse {
    animation: task-updated-pulse 0.6s ease-in-out;
  }
  
  @keyframes value-changed {
    0% { color: inherit; }
    50% { color: #3b82f6; }
    100% { color: inherit; }
  }
  .value-changed {
    animation: value-changed 0.8s ease-in-out;
  }
`

  // Consolidated style injection - only run once
  useEffect(() => {
    const combinedStyles = `
      ${taskUpdatedPulseStyle}

      @keyframes float-slow {
        0%, 100% {
          transform: translateY(0) translateX(0);
        }
        50% {
          transform: translateY(-20px) translateX(10px);
        }
      }
      @keyframes float-slower {
        0%, 100% {
          transform: translateY(0) translateX(0);
        }
        50% {
          transform: translateY(15px) translateX(-10px);
        }
      }
      @keyframes float-medium {
        0%, 100% {
          transform: translateY(0) translateX(0);
        }
        50% {
          transform: translateY(10px) translateX(15px);
        }
      }
      @keyframes gradient-shift {
        0%, 100% {
          opacity: 0.2;
        }
        50% {
          opacity: 0.4;
        }
      }
      .animate-float-slow {
        animation: float-slow 15s ease-in-out infinite;
      }
      .animate-float-slower {
        animation: float-slower 20s ease-in-out infinite;
      }
      .animate-float-medium {
        animation: float-medium 12s ease-in-out infinite;
      }
      .animate-gradient-shift {
        animation: gradient-shift 10s ease-in-out infinite;
      }
    `

    const styleElement = document.createElement("style")
    styleElement.id = "goals-page-styles"
    styleElement.textContent = combinedStyles

    // Only add if not already present
    if (!document.getElementById("goals-page-styles")) {
      document.head.appendChild(styleElement)
    }

    return () => {
      const existingStyle = document.getElementById("goals-page-styles")
      if (existingStyle) {
        document.head.removeChild(existingStyle)
      }
    }
  }, []) // Empty dependency array - only run once

  // Task completion handler - defined outside useEffect
  const handleTaskCompletion = useCallback((event) => {
    const { taskId, goalId, status } = event.detail || {}

    if (taskId && goalId && status === "completed") {
      toast({
        title: "Task Completed",
        description: "Task marked as completed",
      })

      // Trigger subtle animation on stats cards
      const statsCards = document.querySelectorAll(".stats-card")
      statsCards.forEach((card) => {
        card.classList.add("task-updated-pulse")
        setTimeout(() => card.classList.remove("task-updated-pulse"), 1000)
      })
    }
  }, [toast])

  // Listen for task completion events
  useEffect(() => {
    window.addEventListener("task-completed", handleTaskCompletion)

    return () => {
      window.removeEventListener("task-completed", handleTaskCompletion)
    }
  }, [handleTaskCompletion])

  // Function to update UI elements in real-time without full re-render
  const updateUIElements = (goalId, taskId, isCompleted) => {
    // Find and update specific UI elements
    if (goalId) {
      // Update progress bar for the specific goal
      const progressBar = document.querySelector(`[data-goal-id="${goalId}"] .progress-bar`)
      if (progressBar) {
        // Get current goal from state
        const goal = goals.find((g) => g.id === goalId)
        if (goal) {
          // Update width of progress bar
          progressBar.style.width = `${goal.progress}%`

          // Update progress text
          const progressText = document.querySelector(`[data-goal-id="${goalId}"] .progress-text`)
          if (progressText) {
            progressText.textContent = `${goal.progress}%`
          }

          // Update task counter
          const taskCounter = document.querySelector(`[data-goal-id="${goalId}"] .task-counter`)
          if (taskCounter) {
            const completedTasks = goal.tasks?.filter((t) => t.status === "completed").length || 0
            const totalTasks = goal.tasks?.length || 0
            taskCounter.textContent = `${completedTasks}/${totalTasks}`
          }
        }
      }
    }

    // Update specific task item if taskId is provided
    if (taskId) {
      const taskItem = document.querySelector(`[data-task-id="${taskId}"]`)
      if (taskItem) {
        if (isCompleted) {
          taskItem.classList.add("completed")
          taskItem.setAttribute("data-status", "completed")
        } else {
          taskItem.classList.remove("completed")
          taskItem.setAttribute("data-status", "in-progress")
        }
      }
    }

    // Update global stats counters
    document.querySelectorAll(".stats-card").forEach((card) => {
      const statType = card.getAttribute("data-stat-type")
      if (statType && goalStats[statType] !== undefined) {
        const counterElement = card.querySelector(".counter")
        if (counterElement) {
          counterElement.textContent = goalStats[statType]

          // Add animation to highlight the change
          counterElement.classList.add("value-changed")
          setTimeout(() => counterElement.classList.remove("value-changed"), 1000)
        }
      }
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto py-12 px-4">
          <div className="max-w-6xl mx-auto space-y-12">
            <h1 className="text-4xl md:text-5xl font-light tracking-tight text-foreground">Goals</h1>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="border-0 bg-muted/30 animate-pulse">
                  <CardContent className="p-6">
                    <div className="h-6 bg-muted/50 rounded w-1/2 mb-3"></div>
                    <div className="h-8 bg-muted/50 rounded w-1/3"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="space-y-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="border-0 bg-muted/20 animate-pulse">
                  <CardContent className="p-8">
                    <div className="h-6 bg-muted/50 rounded w-3/4 mb-6"></div>
                    <div className="h-4 bg-muted/50 rounded w-full mb-3"></div>
                    <div className="h-4 bg-muted/50 rounded w-5/6"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Filter goals based on search and filters
  const filteredGoals = goals.filter(goal => {
    const matchesSearch = goal.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         goal.description?.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "all" || goal.category === selectedCategory
    const matchesPriority = selectedPriority === "all" || goal.priority === selectedPriority

    return matchesSearch && matchesCategory && matchesPriority
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto py-8 px-4">
        <motion.div
          className="max-w-7xl mx-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {/* Appointy-style layout with sidebar */}
          <div className="flex gap-8">
            {/* Sidebar */}
            <div className="w-80 flex-shrink-0 hidden lg:block">
              <GoalsSidebar
                goals={goals}
                selectedCategory={selectedCategory}
                selectedPriority={selectedPriority}
                onCategoryChange={setSelectedCategory}
                onPriorityChange={setSelectedPriority}
              />
            </div>

            {/* Main Content */}
            <div className="flex-1 space-y-8">

          {/* Appointy-style Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-6 mb-8">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className={`h-12 w-12 rounded-full bg-gradient-to-r ${levelInfo.color} flex items-center justify-center text-white font-bold text-lg shadow-md`}>
                  {prestigeLevel > 0 ? (
                    <Crown className="h-6 w-6" />
                  ) : (
                    level
                  )}
                </div>

                {/* Prestige indicator */}
                {prestigeLevel > 0 && (
                  <div className="absolute -bottom-1 -right-1 h-5 w-5 rounded-full bg-yellow-400 text-xs flex items-center justify-center text-gray-900 font-bold border-2 border-white">
                    P{prestigeLevel}
                  </div>
                )}
              </div>

              <div className="space-y-1">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Goals
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Level {level} • {totalXp} XP • {earnedBadges.length} badges
                </p>
              </div>
            </div>

            {/* Appointy-style action buttons */}
            <div className="flex items-center gap-3">
              <AppointyStyleGoalCreator
                onGoalCreated={handleGoalCreated}
                trigger={
                  <Button className="bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    New Goal
                  </Button>
                }
              />

              <Button
                variant="outline"
                onClick={() => setShowAnalytics(!showAnalytics)}
                className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Analytics
              </Button>
            </div>
          </div>

          {/* Appointy-style filters and search */}
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
            <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    ref={searchInputRef}
                    placeholder="Search goals..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 border-gray-300 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowProfile(true)}
                  className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <Award className="h-4 w-4 mr-2" />
                  Profile
                </Button>

                <Button
                  variant="outline"
                  asChild
                  className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <Link href="/goals/archived">
                    <Archive className="h-4 w-4 mr-2" />
                    Archive
                  </Link>
                </Button>

                <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded-lg">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none border-0"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-none border-0"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "calendar" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("calendar")}
                    className="rounded-l-none border-0"
                  >
                    <Calendar className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Appointy-style Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Goals</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{goalStats.total}</p>
                    <p className="text-xs text-purple-600 dark:text-purple-400">+{goalStats.total * 5} XP</p>
                  </div>
                  <div className="w-12 h-12 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                    <Target className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{goalStats.completed}</p>
                    <p className="text-xs text-green-600 dark:text-green-400">+{goalStats.completed * 50} XP</p>
                  </div>
                  <div className="w-12 h-12 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <CheckCircle2 className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">In Progress</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{goalStats.inProgress}</p>
                    <p className="text-xs text-blue-600 dark:text-blue-400">+{goalStats.inProgress * 10} XP</p>
                  </div>
                  <div className="w-12 h-12 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Overdue</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{goalStats.overdue}</p>
                    <p className="text-xs text-red-600 dark:text-red-400">
                      {goalStats.overdue > 0 ? "Needs attention" : "All on track"}
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                    <Clock className="h-6 w-6 text-red-600 dark:text-red-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>



      {/* Daily Challenges Section */}
      <div className="animate-in fade-in-0 slide-in-from-left-4 duration-500 delay-500">
        <DailyChallenges className="mb-6" />
      </div>

      {showAnalytics && (
        <div className="animate-in fade-in-0 slide-in-from-top-4 duration-500">
          <GoalAnalytics includeArchived={true} />
        </div>
      )}

          {/* Appointy-style Goals Section */}
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Goals
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {filteredGoals.length} of {goals.length} goals
                </p>
              </div>
            </div>

            {/* Goals Content - Appointy style */}
            {viewMode === "calendar" ? (
              <GoalsCalendarView
                goals={filteredGoals}
                onGoalClick={handleViewGoalDetails}
              />
            ) : filteredGoals.length > 0 ? (
              <div className={cn(
                viewMode === "grid"
                  ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                  : "space-y-4"
              )}>
                {filteredGoals.map((goal, index) => (
                  <motion.div
                    key={goal.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <MinimalistGoalCard
                      goal={{
                        ...goal,
                        tasks: goal.tasks || [],
                        estimatedXp: Math.round(50 * (
                          goal.complexity === "expert" ? 2.5 :
                          goal.complexity === "complex" ? 2.0 :
                          goal.complexity === "moderate" ? 1.5 : 1.0
                        ))
                      }}
                      onViewDetails={handleViewGoalDetails}
                      onGoalUpdated={handleGoalUpdated}
                      showXpPreview={true}
                      className={viewMode === "list" ? "max-w-none" : ""}
                    />
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  {searchQuery || selectedCategory !== "all" || selectedPriority !== "all"
                    ? "No goals match your filters"
                    : "No goals yet"}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {searchQuery || selectedCategory !== "all" || selectedPriority !== "all"
                    ? "Try adjusting your search or filters"
                    : "Create your first goal to get started"}
                </p>
                {(!searchQuery && selectedCategory === "all" && selectedPriority === "all") && (
                  <AppointyStyleGoalCreator onGoalCreated={handleGoalCreated} />
                )}
              </div>
            )}
          </div>

      {goalStats.total === 0 && (
        <Card className="minimalist-card border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-16 px-8">
            <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center mb-6">
              <Target className="h-8 w-8 text-muted-foreground/60" />
            </div>
            <h3 className="text-xl font-light text-foreground mb-2">No Goals Yet</h3>
            <p className="text-muted-foreground text-center max-w-sm mb-8 leading-relaxed">
              Create your first goal to start tracking your progress and building momentum toward your aspirations.
            </p>
            <EnhancedGoalCreator onGoalCreated={handleGoalCreated} />
          </CardContent>
        </Card>
      )}

      <Dialog open={showProfile} onOpenChange={setShowProfile}>
        <DialogContent className="sm:max-w-[95vw] lg:max-w-[1200px] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Your Progress Profile</DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto max-h-[calc(90vh-120px)] pr-2">
            <GamificationProfile />
          </div>
        </DialogContent>
      </Dialog>

            </div>
          </div>

          {showLevelUp && <LevelUpCelebration level={newLevel} onClose={() => setShowLevelUp(false)} />}

          {showBadge && earnedBadge && <BadgeEarned badge={earnedBadge} onClose={() => setShowBadge(false)} />}
        </motion.div>
      </div>
    </div>
  )
}
