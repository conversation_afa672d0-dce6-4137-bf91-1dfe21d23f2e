import { AIServiceStatus } from "@/components/ai-service-status"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON>riangle, CheckCircle, Info } from "lucide-react"

export default function EnvStatusPage() {
  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold mb-6">Environment Status</h1>

      <div className="grid gap-6 md:grid-cols-2">
        <AIServiceStatus />

        <Card>
          <CardHeader>
            <CardTitle>Environment Variables</CardTitle>
            <CardDescription>Required and optional environment variables</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Environment Variables</AlertTitle>
              <AlertDescription>
                This page shows the status of environment variables without revealing their values.
              </AlertDescription>
            </Alert>

            <div>
              <h3 className="text-sm font-medium mb-2">Required Variables</h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>SUPABASE_URL / NEXT_PUBLIC_SUPABASE_URL</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>SUPABASE_ANON_KEY / NEXT_PUBLIC_SUPABASE_ANON_KEY</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>SUPABASE_SERVICE_ROLE_KEY</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2">Optional AI Variables</h3>
              <Alert className="mb-2">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>AI Functionality</AlertTitle>
                <AlertDescription>
                  At least one AI service is recommended for optimal functionality. If no AI services are configured,
                  the app will use a fallback assistant.
                </AlertDescription>
              </Alert>
              <ul className="space-y-2 text-sm">
                <li>OPENAI_API_KEY (server-side only)</li>
                <li>GEMINI_API_KEY (server-side only)</li>
                <li>DEEPSEEK_API_KEY (server-side only)</li>
                <li>DEEPSEEK_R1_API_KEY (server-side only)</li>
                <li>DEEPSEEK_V3_API_KEY (server-side only)</li>
                <li>XAI_API_KEY (server-side only)</li>
                <li>LLAMA3_API_KEY (server-side only)</li>
                <li>DEEPHERMES_API_KEY (server-side only)</li>
                <li>OPENROUTER_API_KEY (server-side only)</li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2">Optional Auth Variables</h3>
              <ul className="space-y-2 text-sm">
                <li>GOOGLE_CLIENT_ID / GOOGLE_CLIENT_SECRET / NEXT_PUBLIC_GOOGLE_CLIENT_ID</li>
                <li>MICROSOFT_CLIENT_ID / MICROSOFT_CLIENT_SECRET / NEXT_PUBLIC_MICROSOFT_CLIENT_ID</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Environment Variable Best Practices</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Development Environment</h3>
              <ul className="list-disc pl-5 space-y-1 text-sm">
                <li>
                  Use a <code>.env.local</code> file for local development
                </li>
                <li>
                  Never commit <code>.env.local</code> to version control
                </li>
                <li>
                  Use <code>.env.example</code> with placeholder values as a template
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2">Production Environment</h3>
              <ul className="list-disc pl-5 space-y-1 text-sm">
                <li>Set environment variables through your hosting platform (e.g., Vercel)</li>
                <li>Use different API keys for development and production</li>
                <li>Regularly rotate API keys for security</li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2">Security Considerations</h3>
              <ul className="list-disc pl-5 space-y-1 text-sm">
                <li>Never expose API keys in client-side code (except those prefixed with NEXT_PUBLIC_)</li>
                <li>Use environment variable validation to catch configuration errors early</li>
                <li>Implement proper error handling for missing environment variables</li>
                <li>Use the least privileged API keys possible for each service</li>
                <li>
                  For AI services (OpenAI, Gemini, DeepSeek), use server-side API routes instead of client-side keys
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
