import { type NextRequest, NextResponse } from "next/server"
import { checkShardHealth, getShardMetrics } from "@/lib/sharding/shard-monitor"
import { logger } from "@/lib/api/logger"

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

// Admin middleware would go here in a real application
async function verifyAdminAccess(req: NextRequest) {
  // In a real application, you would verify that the user is an admin
  // For now, we'll just check for an admin token in the headers
  const adminToken = req.headers.get("x-admin-token")

  if (!adminToken || adminToken !== process.env.ADMIN_API_TOKEN) {
    return false
  }

  return true
}

export async function GET(req: NextRequest) {
  // Verify admin access
  const isAdmin = await verifyAdminAccess(req)
  if (!isAdmin) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const searchParams = req.nextUrl.searchParams
    const action = searchParams.get("action") || "health"

    if (action === "health") {
      // Check shard health
      const healthResults = await checkShardHealth()

      return NextResponse.json({
        health: Object.fromEntries(healthResults),
        timestamp: new Date().toISOString(),
      })
    } else if (action === "metrics") {
      // Get shard metrics
      const metricsResults = await getShardMetrics()

      return NextResponse.json({
        metrics: Object.fromEntries(metricsResults),
        timestamp: new Date().toISOString(),
      })
    } else {
      return NextResponse.json({ error: "Invalid action" }, { status: 400 })
    }
  } catch (error: any) {
    logger.error("admin/sharding/health", {
      error: error.message,
    })

    return NextResponse.json({ error: "Failed to check shard health" }, { status: 500 })
  }
}
