/**
 * API Route: /api/ai/gemini/client-status
 *
 * This endpoint provides a secure way for the client to check
 * if the Gemini API is available without exposing the API key.
 */

import { type NextRequest, NextResponse } from "next/server"
import { isAIServiceAvailable } from "@/lib/api/ai-service"

export async function GET(req: NextRequest) {
  try {
    // Check if Gemini is available (server-side only)
    const isAvailable = isAIServiceAvailable("gemini")

    // Return status without exposing the actual API key
    return NextResponse.json({
      available: isAvailable,
      service: "gemini",
    })
  } catch (error) {
    console.error("Error checking Gemini availability:", error)
    return NextResponse.json({ error: "Failed to check Gemini availability" }, { status: 500 })
  }
}
