"use client"

import React, { useState, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday, isBefore, isAfter } from "date-fns"
import { ChevronLeft, ChevronRight, Calendar, Target, Clock } from "lucide-react"
import { motion } from "framer-motion"

interface Goal {
  id: string
  title: string
  description?: string
  progress: number
  category: string
  priority: "low" | "medium" | "high" | "critical"
  targetDate?: string
  createdAt: string
}

interface GoalsCalendarViewProps {
  goals: Goal[]
  onGoalClick: (goalId: string) => void
  className?: string
}

export function GoalsCalendarView({ goals, onGoalClick, className = "" }: GoalsCalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date())

  // Get goals for the current month
  const monthStart = startOfMonth(currentDate)
  const monthEnd = endOfMonth(currentDate)
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd })

  // Group goals by date
  const goalsByDate = useMemo(() => {
    const grouped: Record<string, Goal[]> = {}
    
    goals.forEach(goal => {
      if (goal.targetDate) {
        const dateKey = format(new Date(goal.targetDate), 'yyyy-MM-dd')
        if (!grouped[dateKey]) {
          grouped[dateKey] = []
        }
        grouped[dateKey].push(goal)
      }
    })
    
    return grouped
  }, [goals])

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical": return "bg-red-500"
      case "high": return "bg-orange-500"
      case "medium": return "bg-blue-500"
      case "low": return "bg-green-500"
      default: return "bg-gray-500"
    }
  }

  const getStatusColor = (goal: Goal) => {
    if (goal.progress === 100) return "bg-green-500"
    if (goal.targetDate && isBefore(new Date(goal.targetDate), new Date()) && goal.progress < 100) {
      return "bg-red-500"
    }
    return getPriorityColor(goal.priority)
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Calendar Header */}
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-purple-600" />
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Goals Calendar
              </CardTitle>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('prev')}
                className="border-gray-300 dark:border-gray-600"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <div className="px-4 py-2 text-lg font-semibold text-gray-900 dark:text-gray-100 min-w-[140px] text-center">
                {format(currentDate, 'MMMM yyyy')}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('next')}
                className="border-gray-300 dark:border-gray-600"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {/* Day headers */}
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400">
                {day}
              </div>
            ))}

            {/* Calendar days */}
            {monthDays.map((day, index) => {
              const dateKey = format(day, 'yyyy-MM-dd')
              const dayGoals = goalsByDate[dateKey] || []
              const isCurrentDay = isToday(day)
              
              return (
                <motion.div
                  key={day.toISOString()}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2, delay: index * 0.01 }}
                  className={cn(
                    "min-h-[100px] p-2 border border-gray-200 dark:border-gray-700 rounded-lg",
                    "hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",
                    isCurrentDay && "bg-purple-50 dark:bg-purple-900/20 border-purple-300 dark:border-purple-600"
                  )}
                >
                  {/* Day number */}
                  <div className={cn(
                    "text-sm font-medium mb-2",
                    isCurrentDay ? "text-purple-600 dark:text-purple-400" : "text-gray-900 dark:text-gray-100"
                  )}>
                    {format(day, 'd')}
                  </div>

                  {/* Goals for this day */}
                  <div className="space-y-1">
                    {dayGoals.slice(0, 3).map(goal => (
                      <motion.div
                        key={goal.id}
                        whileHover={{ scale: 1.02 }}
                        onClick={() => onGoalClick(goal.id)}
                        className={cn(
                          "p-2 rounded-md cursor-pointer text-xs",
                          "hover:shadow-sm transition-all duration-200",
                          "border border-gray-200 dark:border-gray-600",
                          goal.progress === 100 
                            ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                            : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                        )}
                      >
                        <div className="flex items-center gap-1 mb-1">
                          <div className={cn(
                            "w-2 h-2 rounded-full flex-shrink-0",
                            getStatusColor(goal)
                          )} />
                          <span className="font-medium truncate">{goal.title}</span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <Badge 
                            variant="outline" 
                            className="text-xs px-1 py-0"
                          >
                            {goal.category}
                          </Badge>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {goal.progress}%
                          </span>
                        </div>
                      </motion.div>
                    ))}
                    
                    {dayGoals.length > 3 && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-1">
                        +{dayGoals.length - 3} more
                      </div>
                    )}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Legend</h3>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-500" />
              <span className="text-gray-600 dark:text-gray-400">Completed</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500" />
              <span className="text-gray-600 dark:text-gray-400">Overdue</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-orange-500" />
              <span className="text-gray-600 dark:text-gray-400">High Priority</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-blue-500" />
              <span className="text-gray-600 dark:text-gray-400">Medium Priority</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
