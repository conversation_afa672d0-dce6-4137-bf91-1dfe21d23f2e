@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Luxury-specific styles */
.luxury-shadow {
  box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.1);
}

.dark .luxury-shadow {
  box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.3);
}

/* Custom scrollbar for luxury feel */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
  border: transparent;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

/* Smooth transitions for theme changes */
html {
  transition: background-color 0.3s ease;
}

/* Subtle text shadow for headings in dark mode */
.dark h1,
.dark h2,
.dark h3 {
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Futuristic focus styles */
:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  transition: outline-offset 0.1s ease;
}

/* Subtle text selection styling */
::selection {
  background: hsl(var(--primary) / 0.2);
}

/* Appointy-inspired Design Utilities */
.appointy-card {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl;
  @apply shadow-sm hover:shadow-lg transition-all duration-300 ease-out;
  @apply hover:border-purple-300 dark:hover:border-purple-600;
  @apply hover:-translate-y-1;
}

.appointy-button-primary {
  @apply bg-purple-600 hover:bg-purple-700 text-white font-medium;
  @apply rounded-lg shadow-sm hover:shadow-md transition-all duration-200;
  @apply focus:ring-2 focus:ring-purple-500 focus:ring-offset-2;
}

.appointy-button-secondary {
  @apply border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700;
  @apply text-gray-700 dark:text-gray-300 font-medium;
  @apply rounded-lg transition-all duration-200;
}

.appointy-input {
  @apply border-gray-300 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500;
  @apply rounded-lg transition-all duration-200;
}

.appointy-progress {
  @apply bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.appointy-progress-fill {
  @apply h-full rounded-full transition-all duration-500;
}

.appointy-badge {
  @apply inline-flex items-center px-2 py-1 rounded-md text-xs font-medium;
  @apply border transition-colors duration-200;
}

/* Legacy minimalist classes for backward compatibility */
.minimalist-card {
  @apply appointy-card;
}

.minimalist-card-hover {
  @apply hover:translate-y-[-2px] hover:shadow-lg;
}

.minimalist-progress {
  @apply appointy-progress;
}

.minimalist-progress-fill {
  @apply appointy-progress-fill bg-gradient-to-r from-purple-500 to-blue-500;
}

.minimalist-badge {
  @apply appointy-badge bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700;
}

/* Appointy-inspired Typography Hierarchy */
.appointy-text-h1 {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100;
}

.appointy-text-h2 {
  @apply text-xl font-semibold text-gray-900 dark:text-gray-100;
}

.appointy-text-h3 {
  @apply text-lg font-medium text-gray-900 dark:text-gray-100;
}

.appointy-text-body {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.appointy-text-caption {
  @apply text-xs text-gray-500 dark:text-gray-500;
}

/* Legacy minimalist text classes */
.minimalist-text-hierarchy-1 {
  @apply appointy-text-h3;
}

.minimalist-text-hierarchy-2 {
  @apply appointy-text-body;
}

.minimalist-text-hierarchy-3 {
  @apply appointy-text-caption;
}

.minimalist-button {
  @apply appointy-button-primary;
}

.minimalist-badge-priority-high {
  @apply bg-red-500/10 text-red-600 border-red-500/20;
}

.minimalist-badge-priority-medium {
  @apply bg-blue-500/10 text-blue-600 border-blue-500/20;
}

.minimalist-badge-priority-low {
  @apply bg-green-500/10 text-green-600 border-green-500/20;
}

.minimalist-badge-priority-critical {
  @apply bg-purple-500/10 text-purple-600 border-purple-500/20;
}

.minimalist-text-hierarchy-1 {
  @apply text-lg font-semibold text-foreground leading-tight;
}

.minimalist-text-hierarchy-2 {
  @apply text-sm text-muted-foreground leading-relaxed;
}

.minimalist-text-hierarchy-3 {
  @apply text-xs text-muted-foreground/80;
}

.minimalist-button {
  @apply bg-gradient-to-r from-emerald-500 to-teal-500 text-white;
  @apply hover:from-emerald-600 hover:to-teal-600 transition-all duration-300;
  @apply shadow-sm hover:shadow-md rounded-lg;
  @apply border-0 focus:ring-2 focus:ring-emerald-500/20 focus:ring-offset-2;
}

/* Minimalist card hover effects */
.minimalist-card:hover {
  @apply shadow-lg shadow-black/5;
  transform: translateY(-2px);
}

/* Smooth focus states */
.minimalist-card:focus-within {
  @apply ring-2 ring-emerald-500/20 ring-offset-2;
}

/* Refined text selection */
.minimalist-card ::selection {
  @apply bg-emerald-500/20;
}
